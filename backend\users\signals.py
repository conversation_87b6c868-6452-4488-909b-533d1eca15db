from django.db.models.signals import post_save
from django.dispatch import receiver
from users.models import User
from core.services.email_service import EmailService

@receiver(post_save, sender=User)
def queue_user_emails(sender, instance, created, **kwargs):
    """Queue emails when a user is created"""
    if created:
        # Queue verification email for new users
        EmailService.queue_verification_email(instance)