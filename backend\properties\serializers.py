from rest_framework import serializers
from .models import Property, Image, Inquiry, Transaction, Favorite
from users.models import User, Role
from drf_spectacular.utils import extend_schema_field
from typing import Optional

# Simple user serializer to avoid circular imports
class SimpleUserSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source='role.role_name', read_only=True)
    profile_image_url = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['user_id', 'name', 'email', 'phone_number', 'role_name', 'profile_image_url']
        read_only_fields = ['user_id']

    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_profile_image_url(self, obj) -> Optional[str]:
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

class ImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethod<PERSON>ield()
    
    class Meta:
        model = Image
        fields = ['image_id', 'property', 'image_url', 'caption', 'is_primary', 'uploaded_at']
    
    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_image_url(self, obj) -> Optional[str]:
        return obj.get_image_url()

class PropertyListSerializer(serializers.ModelSerializer):
    """Serializer for listing properties without images"""
    agent = SimpleUserSerializer(read_only=True)
    primary_image = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = [
            'property_id', 'title', 'description', 'price', 'location',
            'size', 'type', 'agent', 'created_at', 'updated_at',
            'bedrooms', 'listing_type', 'status', 'primary_image', 'is_featured'
        ]
        read_only_fields = ['property_id', 'created_at', 'updated_at']
    
    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_primary_image(self, obj) -> Optional[str]:
        primary_image = obj.get_primary_image()
        if primary_image:
            return primary_image.get_image_url()
        return None

class PropertySerializer(serializers.ModelSerializer):
    agent = SimpleUserSerializer(read_only=True)
    agent_id = serializers.PrimaryKeyRelatedField(
        source='agent',
        queryset=User.objects.filter(role__role_name='AGENT'),
        write_only=True
    )

    class Meta:
        model = Property
        fields = [
            'property_id', 'title', 'description', 'price', 'location',
            'size', 'type', 'agent', 'agent_id',
            'created_at', 'updated_at', 'bedrooms', 'listing_type', 'status', 'is_featured'
        ]
        read_only_fields = ['property_id', 'created_at', 'updated_at']

class PropertyDetailSerializer(serializers.ModelSerializer):
    """Detailed property serializer with images - used only when specifically needed"""
    agent = SimpleUserSerializer(read_only=True)
    agent_id = serializers.PrimaryKeyRelatedField(
        source='agent',
        queryset=User.objects.filter(role__role_name='AGENT'),
        write_only=True
    )
    images = ImageSerializer(many=True, read_only=True)

    class Meta:
        model = Property
        fields = [
            'property_id', 'title', 'description', 'price', 'location',
            'size', 'type', 'agent', 'agent_id', 'images',
            'created_at', 'updated_at', 'bedrooms', 'listing_type', 'status', 'is_featured'
        ]
        read_only_fields = ['property_id', 'created_at', 'updated_at']

class SimpleInquiryPropertySerializer(serializers.ModelSerializer):
    """Simplified property serializer for inquiries to avoid circular dependencies"""
    agent_name = serializers.CharField(source='agent.name', read_only=True)

    class Meta:
        model = Property
        fields = [
            'property_id', 'title', 'description', 'price', 'location',
            'size', 'type', 'agent_name', 'bedrooms', 'listing_type', 'status'
        ]

class InquirySerializer(serializers.ModelSerializer):
    client = SimpleUserSerializer(read_only=True)
    client_id = serializers.PrimaryKeyRelatedField(
        source='client',
        queryset=User.objects.filter(role__role_name='CLIENT'),
        write_only=True
    )
    property = SimpleInquiryPropertySerializer(read_only=True)
    property_id = serializers.PrimaryKeyRelatedField(
        source='property',
        queryset=Property.objects.all(),
        write_only=True
    )

    class Meta:
        model = Inquiry
        fields = [
            'inquiry_id', 'property', 'property_id', 'client',
            'client_id', 'message', 'status', 'created_at', 'updated_at',
            'is_viewing_request', 'requested_date', 'requested_time', 'viewing_status'
        ]
        read_only_fields = ['client', 'created_at']

class TransactionSerializer(serializers.ModelSerializer):
    client = SimpleUserSerializer(read_only=True)
    property = SimpleInquiryPropertySerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True)
    property_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Transaction
        fields = ['transaction_id', 'client', 'property', 'client_id', 'property_id', 
                 'amount', 'status', 'transaction_date', 'created_at']
        read_only_fields = ['transaction_id', 'created_at']

    def validate(self, data):
        """
        Custom validation for transaction data
        """
        # Validate that the client exists and has CLIENT role
        client_id = data.get('client_id')
        if client_id:
            try:
                client = User.objects.get(id=client_id)
                if not client.role.role_name == 'CLIENT':
                    raise serializers.ValidationError("Only clients can create transactions")
            except User.DoesNotExist:
                raise serializers.ValidationError("Client does not exist")

        # Validate that the property exists
        property_id = data.get('property_id')
        if property_id:
            try:
                property = Property.objects.get(property_id=property_id)
                # Validate that the amount is not greater than the property price
                if data.get('amount', 0) > property.price:
                    raise serializers.ValidationError("Transaction amount cannot exceed property price")
            except Property.DoesNotExist:
                raise serializers.ValidationError("Property does not exist")

        return data

    def create(self, validated_data):
        """
        Create a new transaction with validated data
        """
        client_id = validated_data.pop('client_id')
        property_id = validated_data.pop('property_id')
        
        client = User.objects.get(id=client_id)
        property = Property.objects.get(property_id=property_id)
        
        return Transaction.objects.create(
            client=client,
            property=property,
            **validated_data
        )

class SimpleFavoritePropertySerializer(serializers.ModelSerializer):
    """Simplified property serializer for favorites to avoid circular dependencies"""
    agent_name = serializers.CharField(source='agent.name', read_only=True)
    primary_image = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            'property_id', 'title', 'description', 'price', 'location',
            'size', 'type', 'agent_name', 'bedrooms', 'listing_type',
            'status', 'primary_image', 'is_featured'
        ]

    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_primary_image(self, obj) -> Optional[str]:
        primary_image = obj.get_primary_image()
        if primary_image:
            return primary_image.get_image_url()
        return None

class FavoriteSerializer(serializers.ModelSerializer):
    property_details = SimpleFavoritePropertySerializer(source='property', read_only=True)
    is_available = serializers.SerializerMethodField()

    class Meta:
        model = Favorite
        fields = ['id', 'property', 'property_details', 'created_at', 'is_available']
        read_only_fields = ['user', 'created_at']

    @extend_schema_field(serializers.BooleanField())
    def get_is_available(self, obj) -> bool:
        return obj.property.status == 'APPROVED'

class FavoritePropertyListSerializer(serializers.ModelSerializer):
    primary_image = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            'property_id', 'title', 'description', 'price', 'location',
            'size', 'type', 'created_at', 'updated_at',
            'bedrooms', 'listing_type', 'status', 'primary_image', 'is_featured'
        ]

    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_primary_image(self, obj) -> Optional[str]:
        primary_image = obj.get_primary_image()
        if primary_image:
            return primary_image.get_image_url()
        return None
