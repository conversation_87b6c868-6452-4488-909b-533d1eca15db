# Generated by Django 5.1.7 on 2025-05-13 08:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0006_property_status_alter_image_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='Amenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('icon', models.CharField(blank=True, help_text="CSS icon class (e.g., 'bi-check-circle-fill')", max_length=50)),
            ],
            options={
                'verbose_name_plural': 'Amenities',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Attraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('distance', models.DecimalField(blank=True, decimal_places=2, help_text='Distance in kilometers', max_digits=5, null=True)),
                ('icon', models.CharField(blank=True, help_text="CSS icon class (e.g., 'bi-check-circle-fill')", max_length=50)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='property',
            name='amenities',
            field=models.ManyToManyField(blank=True, related_name='properties', to='properties.amenity'),
        ),
        migrations.AddField(
            model_name='property',
            name='attractions',
            field=models.ManyToManyField(blank=True, related_name='properties', to='properties.attraction'),
        ),
    ]
