from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import Role
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Seeds the database with initial users'

    def handle(self, *args, **kwargs):
        self.stdout.write('Seeding users...')
        User = get_user_model()

        # Create superuser if it doesn't exist
        if not User.objects.filter(email='<EMAIL>').exists():
            admin_role, _ = Role.objects.get_or_create(role_name='ADMIN')
            admin_user = User.objects.create_user(
                email='<EMAIL>',
                name='Admin User',
                password='#af673b#@43a66',
                role=admin_role
            )
            admin_user.is_superuser = True
            admin_user.is_staff = True
            admin_user.save()
            self.stdout.write(self.style.SUCCESS('Created superuser: admin'))
        else:
            self.stdout.write(self.style.WARNING('Superuser already exists'))

        # Create API JWT user if it doesn't exist
        if not User.objects.filter(email='<EMAIL>').exists():
            client_role, _ = Role.objects.get_or_create(role_name='CLIENT')
            api_user = User.objects.create_user(
                email='<EMAIL>',
                name='API User',
                password='&(!6af673b#@43a',  # You should change this in production
                role=client_role
            )
            self.stdout.write(self.style.SUCCESS('Created API user: api_user'))
        else:
            self.stdout.write(self.style.WARNING('API user already exists'))

        self.stdout.write(self.style.SUCCESS('User seeding completed!')) 