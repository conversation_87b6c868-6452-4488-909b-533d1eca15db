from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.urls import reverse_lazy
from django.views.generic import ListView
from django.utils.decorators import method_decorator
from django.db.models import Count, Sum
from django.http import HttpResponse
import csv
from datetime import datetime, timedelta
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet

from properties.models import Property, Inquiry, Transaction
from django.contrib.auth import get_user_model

User = get_user_model()

@method_decorator(staff_member_required(login_url=reverse_lazy('web:signin')), name='dispatch')
class PropertyReportView(ListView):
    model = Property
    template_name = 'reports/property_report.html'
    context_object_name = 'properties'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Apply filters
        agent_id = self.request.GET.get('agent')
        property_type = self.request.GET.get('type')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        if agent_id:
            queryset = queryset.filter(agent_id=agent_id)
        if property_type:
            queryset = queryset.filter(type=property_type)
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['agents'] = User.objects.filter(role__role_name='AGENT')
        context['property_types'] = Property.objects.values_list('type', flat=True).distinct()
        return context

@method_decorator(staff_member_required(login_url=reverse_lazy('web:signin')), name='dispatch')
class InquiryReportView(ListView):
    model = Inquiry
    template_name = 'reports/inquiry_report.html'
    context_object_name = 'inquiries'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Apply filters
        status = self.request.GET.get('status')
        property_id = self.request.GET.get('property')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        if status:
            queryset = queryset.filter(status=status)
        if property_id:
            queryset = queryset.filter(property_id=property_id)
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['properties'] = Property.objects.all()
        context['statuses'] = Inquiry.objects.values_list('status', flat=True).distinct()
        return context

@method_decorator(staff_member_required(login_url=reverse_lazy('web:signin')), name='dispatch')
class TransactionReportView(ListView):
    model = Transaction
    template_name = 'reports/transaction_report.html'
    context_object_name = 'transactions'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Apply filters
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        if status:
            queryset = queryset.filter(status=status)
        if date_from:
            queryset = queryset.filter(transaction_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(transaction_date__lte=date_to)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['statuses'] = Transaction.objects.values_list('status', flat=True).distinct()
        return context

# Export views
@staff_member_required(login_url=reverse_lazy('web:signin'))
def export_property_csv(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="properties_report_{datetime.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['ID', 'Title', 'Type', 'Price', 'Location', 'Agent', 'Created At'])
    
    # Apply the same filters as in the list view
    properties = Property.objects.all()
    agent_id = request.GET.get('agent')
    property_type = request.GET.get('type')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if agent_id:
        properties = properties.filter(agent_id=agent_id)
    if property_type:
        properties = properties.filter(type=property_type)
    if date_from:
        properties = properties.filter(created_at__gte=date_from)
    if date_to:
        properties = properties.filter(created_at__lte=date_to)
    
    for prop in properties:
        writer.writerow([
            prop.property_id, 
            prop.title, 
            prop.type, 
            prop.price, 
            prop.location, 
            prop.agent.get_full_name() if prop.agent else 'N/A',
            prop.created_at
        ])
    
    return response

@staff_member_required(login_url=reverse_lazy('web:signin'))
def export_property_pdf(request):
    # Apply the same filters as in the list view
    properties = Property.objects.all()
    agent_id = request.GET.get('agent')
    property_type = request.GET.get('type')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if agent_id:
        properties = properties.filter(agent_id=agent_id)
    if property_type:
        properties = properties.filter(type=property_type)
    if date_from:
        properties = properties.filter(created_at__gte=date_from)
    if date_to:
        properties = properties.filter(created_at__lte=date_to)
    
    # Create a file-like buffer to receive PDF data
    buffer = io.BytesIO()
    
    # Create the PDF object, using the buffer as its "file"
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    elements = []
    
    # Add a title
    styles = getSampleStyleSheet()
    elements.append(Paragraph("Property Report", styles['Title']))
    elements.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal']))
    
    # Create table data
    data = [['ID', 'Title', 'Type', 'Price', 'Location', 'Agent', 'Created At']]
    for prop in properties:
        data.append([
            str(prop.property_id), 
            prop.title, 
            prop.type, 
            str(prop.price), 
            prop.location, 
            prop.agent.get_full_name() if prop.agent else 'N/A',
            prop.created_at.strftime('%Y-%m-%d')
        ])
    
    # Create the table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    
    # Build the PDF
    doc.build(elements)
    
    # Get the value of the BytesIO buffer and write it to the response
    pdf = buffer.getvalue()
    buffer.close()
    
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="properties_report_{datetime.now().strftime("%Y%m%d")}.pdf"'
    response.write(pdf)
    
    return response

# Similar export functions for Inquiries and Transactions
@staff_member_required(login_url=reverse_lazy('web:signin'))
def export_inquiry_csv(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="inquiries_report_{datetime.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['ID', 'Property', 'Client', 'Status', 'Created At'])
    
    inquiries = Inquiry.objects.all()
    status = request.GET.get('status')
    property_id = request.GET.get('property')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if status:
        inquiries = inquiries.filter(status=status)
    if property_id:
        inquiries = inquiries.filter(property_id=property_id)
    if date_from:
        inquiries = inquiries.filter(created_at__gte=date_from)
    if date_to:
        inquiries = inquiries.filter(created_at__lte=date_to)
    
    for inquiry in inquiries:
        writer.writerow([
            inquiry.inquiry_id,
            inquiry.property.title,
            inquiry.client.get_full_name() if inquiry.client else 'N/A',
            inquiry.status,
            inquiry.created_at
        ])
    
    return response

@staff_member_required(login_url=reverse_lazy('web:signin'))
def export_transaction_csv(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="transactions_report_{datetime.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['ID', 'Client', 'Property', 'Amount', 'Status', 'Transaction Date'])
    
    transactions = Transaction.objects.all()
    status = request.GET.get('status')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if status:
        transactions = transactions.filter(status=status)
    if date_from:
        transactions = transactions.filter(transaction_date__gte=date_from)
    if date_to:
        transactions = transactions.filter(transaction_date__lte=date_to)
    
    for transaction in transactions:
        writer.writerow([
            transaction.transaction_id,
            transaction.client.get_full_name() if transaction.client else 'N/A',
            transaction.property.title,
            transaction.amount,
            transaction.status,
            transaction.transaction_date
        ])
    
    return response
