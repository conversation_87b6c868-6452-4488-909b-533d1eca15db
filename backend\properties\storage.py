from django.core.files.storage import Storage
from django.utils.deconstruct import deconstructible

@deconstructible
class Base64Storage(Storage):
    """Storage backend that stores files as base64 strings in the database."""
    
    def _open(self, name, mode='rb'):
        # This method isn't needed for base64 storage
        pass
    
    def _save(self, name, content):
        # Just return the name as we'll store the content in the model
        return name
    
    def delete(self, name):
        # No files to delete
        pass
    
    def exists(self, name):
        # Always return False so save() will always work
        return False
    
    def url(self, name):
        # The URL is the base64 data itself, which is stored in the model
        return name

# Create a single instance to use
base64_storage = Base64Storage()


