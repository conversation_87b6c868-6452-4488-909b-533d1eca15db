"""
Custom DRF Spectacular extensions to handle serialization issues
"""
from drf_spectacular.extensions import OpenApiSerializerExtension
from drf_spectacular.plumbing import build_array_type, build_basic_type
from drf_spectacular.types import OpenApiTypes
from rest_framework import serializers


class SafeListSerializerExtension(OpenApiSerializerExtension):
    """
    Extension to safely handle ListSerializer instances that cause JSON serialization issues
    """
    target_class = 'rest_framework.serializers.ListSerializer'
    
    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for ListSerializer to avoid JSON serialization issues
        """
        try:
            # Get the child serializer
            child_serializer = self.target.child
            
            # Map the child serializer
            child_schema = auto_schema._map_serializer(child_serializer, direction)
            
            # Return an array type with the child schema
            return build_array_type(child_schema)
            
        except Exception:
            # Fallback to a basic array type
            return build_array_type(build_basic_type(OpenApiTypes.OBJECT))


class ImageSerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle ImageSerializer with many=True safely
    """
    target_class = 'properties.serializers.ImageSerializer'
    
    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for ImageSerializer to ensure proper schema generation
        """
        # Use the default mapping but ensure it's safe
        return auto_schema._map_basic_serializer(self.target, direction)


class PropertySerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle PropertySerializer safely
    """
    target_class = 'properties.serializers.PropertySerializer'
    
    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for PropertySerializer to avoid circular dependencies
        """
        # Use the default mapping
        return auto_schema._map_basic_serializer(self.target, direction)


class FavoriteSerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle FavoriteSerializer safely
    """
    target_class = 'properties.serializers.FavoriteSerializer'

    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for FavoriteSerializer to avoid circular dependencies
        """
        # Use the default mapping
        return auto_schema._map_basic_serializer(self.target, direction)


class AmenitySerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle AmenitySerializer safely
    """
    target_class = 'properties.serializers.AmenitySerializer'

    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for AmenitySerializer to ensure proper schema generation
        """
        return auto_schema._map_basic_serializer(self.target, direction)


class AttractionSerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle AttractionSerializer safely
    """
    target_class = 'properties.serializers.AttractionSerializer'

    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for AttractionSerializer to ensure proper schema generation
        """
        return auto_schema._map_basic_serializer(self.target, direction)


class PropertyDetailSerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle PropertyDetailSerializer safely
    """
    target_class = 'properties.serializers.PropertyDetailSerializer'

    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for PropertyDetailSerializer to avoid circular dependencies
        """
        return auto_schema._map_basic_serializer(self.target, direction)


class MobilePropertyCreateSerializerExtension(OpenApiSerializerExtension):
    """
    Extension to handle MobilePropertyCreateSerializer safely
    """
    target_class = 'properties.serializers.MobilePropertyCreateSerializer'

    def map_serializer(self, auto_schema, direction):
        """
        Custom mapping for MobilePropertyCreateSerializer to ensure proper schema generation
        """
        return auto_schema._map_basic_serializer(self.target, direction)
