from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .models import User, Role
from .serializers import UserSerializer, RoleSerializer, AgentProfileSerializer, AgentCreationSerializer
from django.http import HttpResponse

def health_check(request):
    return HttpResponse("OK", status=200)

# Create your views here.

class UserViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing users
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create_agent':
            return AgentCreationSerializer
        return UserSerializer

    def get_permissions(self):
        """Set permissions based on action"""
        if self.action in ['create_agent', 'bulk_create_agents']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    @extend_schema(
        description='List all users in the system',
        responses={200: UserSerializer(many=True)},
        tags=['Users']
    )
    def list(self, request):
        """
        Get a list of all users.
        """
        users = self.get_queryset()
        serializer = self.get_serializer(users, many=True)
        return Response(serializer.data)

    @extend_schema(
        description='Create a new user',
        request=UserSerializer,
        responses={201: UserSerializer},
        tags=['Users']
    )
    def create(self, request):
        """
        Create a new user in the system.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @extend_schema(
        description='Create a new agent',
        request=AgentCreationSerializer,
        responses={
            201: {
                'type': 'object',
                'properties': {
                    'message': {'type': 'string', 'example': 'Agent created successfully'},
                    'agent': AgentCreationSerializer
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'email': {'type': 'array', 'items': {'type': 'string'}, 'example': ['A user with this email already exists.']},
                    'password': {'type': 'array', 'items': {'type': 'string'}, 'example': ['Password must be at least 8 characters long.']},
                    'confirm_password': {'type': 'array', 'items': {'type': 'string'}, 'example': ['Passwords don\'t match.']},
                    'role_id': {'type': 'array', 'items': {'type': 'string'}, 'example': ['Only AGENT role is allowed for agent creation.']}
                }
            },
            401: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'example': 'Authentication credentials were not provided.'}
                }
            },
            403: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'example': 'You do not have permission to perform this action.'}
                }
            }
        },
        tags=['Agents']
    )
    @action(detail=False, methods=['post'], url_path='create-agent')
    def create_agent(self, request):
        """
        Create a new agent user.
        
        Required fields:
        - name: Full name of the agent
        - email: Email address (must be unique)
        - password: Password (min 8 characters)
        - confirm_password: Password confirmation
        - role_id: Role ID (must be AGENT role)
        
        Optional fields:
        - phone_number: Phone number
        - agent_description: Agent biography
        - profile_image: Profile image file
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        agent = serializer.save()
        
        return Response({
            'message': 'Agent created successfully',
            'agent': AgentCreationSerializer(agent, context={'request': request}).data
        }, status=status.HTTP_201_CREATED)

    @extend_schema(
        description='Create multiple agents in bulk',
        request=AgentCreationSerializer(many=True),
        responses={
            201: {
                'type': 'object',
                'properties': {
                    'message': {'type': 'string', 'example': '2 agents created successfully'},
                    'agents': AgentCreationSerializer(many=True)
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'email': {'type': 'array', 'items': {'type': 'string'}, 'example': ['A user with this email already exists.']},
                    'password': {'type': 'array', 'items': {'type': 'string'}, 'example': ['Password must be at least 8 characters long.']},
                    'confirm_password': {'type': 'array', 'items': {'type': 'string'}, 'example': ['Passwords don\'t match.']},
                    'role_id': {'type': 'array', 'items': {'type': 'string'}, 'example': ['Only AGENT role is allowed for agent creation.']}
                }
            },
            401: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'example': 'Authentication credentials were not provided.'}
                }
            },
            403: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'example': 'You do not have permission to perform this action.'}
                }
            }
        },
        tags=['Agents']
    )
    @action(detail=False, methods=['post'], url_path='bulk-create-agents')
    def bulk_create_agents(self, request):
        """
        Create multiple agents in a single request.
        
        Request body should be an array of agent objects with the same fields as create-agent.
        """
        serializer = self.get_serializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        agents = serializer.save()
        
        return Response({
            'message': f'{len(agents)} agents created successfully',
            'agents': AgentCreationSerializer(agents, many=True, context={'request': request}).data
        }, status=status.HTTP_201_CREATED)

    @extend_schema(
        description='List all agents',
        responses={200: AgentProfileSerializer(many=True)},
        tags=['Agents']
    )
    @action(detail=False, methods=['get'], url_path='agents')
    def list_agents(self, request):
        """
        Get a list of all agents.
        """
        agents = User.objects.filter(role__role_name='AGENT')
        serializer = AgentProfileSerializer(agents, many=True, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        description='Get agent details by ID',
        responses={200: AgentProfileSerializer},
        tags=['Agents']
    )
    @action(detail=True, methods=['get'], url_path='agent-details')
    def agent_details(self, request, pk=None):
        """
        Get detailed information about an agent
        """
        try:
            agent = self.get_object()
            if not agent.is_agent:
                return Response(
                    {"error": "User is not an agent"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            serializer = AgentProfileSerializer(agent, context={'request': request})
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response(
                {"error": "Agent not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @extend_schema(
        description='Get current user profile',
        responses={200: UserSerializer},
        tags=['Users']
    )
    @action(detail=False, methods=['get'], url_path='me')
    def me(self, request):
        """
        Get current user's profile information
        """
        serializer = self.get_serializer(request.user, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        description='Update current user profile',
        request=UserSerializer,
        responses={200: UserSerializer},
        tags=['Users']
    )
    @action(detail=False, methods=['put', 'patch'], url_path='me')
    def update_me(self, request):
        """
        Update current user's profile information
        """
        serializer = self.get_serializer(
            request.user, 
            data=request.data, 
            partial=True,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

class AgentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing agent profiles
    """
    queryset = User.objects.filter(role__role_name='AGENT')
    serializer_class = AgentProfileSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        description='List all agents',
        responses={200: AgentProfileSerializer(many=True)},
        tags=['Agents']
    )
    def list(self, request):
        """
        Get a list of all agents.
        """
        agents = self.get_queryset()
        serializer = self.get_serializer(agents, many=True, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        description='Get agent profile by ID',
        responses={200: AgentProfileSerializer},
        tags=['Agents']
    )
    def retrieve(self, request, pk=None):
        """
        Get detailed information about a specific agent
        """
        try:
            agent = self.get_object()
            serializer = self.get_serializer(agent, context={'request': request})
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response(
                {"error": "Agent not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @extend_schema(
        description='Update agent profile (only for the agent themselves)',
        request=AgentProfileSerializer,
        responses={200: AgentProfileSerializer},
        tags=['Agents']
    )
    def update(self, request, pk=None):
        """
        Update agent profile (only the agent can update their own profile)
        """
        agent = self.get_object()
        
        # Check if the current user is the agent themselves
        if request.user != agent:
            return Response(
                {"error": "You can only update your own profile"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(
            agent, 
            data=request.data, 
            partial=True,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        description='Get current agent profile',
        responses={200: AgentProfileSerializer},
        tags=['Agents']
    )
    @action(detail=False, methods=['get'], url_path='my-profile')
    def my_profile(self, request):
        """
        Get current agent's profile information
        """
        if not request.user.is_agent:
            return Response(
                {"error": "Only agents can access this endpoint"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(request.user, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        description='Update current agent profile',
        request=AgentProfileSerializer,
        responses={200: AgentProfileSerializer},
        tags=['Agents']
    )
    @action(detail=False, methods=['put', 'patch'], url_path='my-profile')
    def update_my_profile(self, request):
        """
        Update current agent's profile information
        """
        if not request.user.is_agent:
            return Response(
                {"error": "Only agents can update their profile"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(
            request.user, 
            data=request.data, 
            partial=True,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

class RoleViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing roles
    """
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        description='List all roles in the system',
        responses={200: RoleSerializer(many=True)},
        tags=['Roles']
    )
    def list(self, request):
        """
        Get a list of all roles.
        """
        roles = self.get_queryset()
        serializer = self.get_serializer(roles, many=True)
        return Response(serializer.data)
