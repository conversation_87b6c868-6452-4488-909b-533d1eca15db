from rest_framework import authentication, exceptions
from rest_framework.permissions import BasePermission
from django.conf import settings
from django.contrib.auth import get_user_model
import jwt
from drf_spectacular.extensions import OpenApiAuthenticationExtension

User = get_user_model()

class JWTAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            return None

        try:
            token = auth_header.split(' ')[1]
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            user = User.objects.get(user_id=payload['user_id'])
            return (user, token)
        except (jwt.InvalidTokenError, User.DoesNotExist):
            return None

class APIKeyAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        api_key = request.META.get('HTTP_X_API_KEY')
        if not api_key:
            return None

        if api_key == settings.API_KEY:
            # For API key auth, we use a system user
            system_user = User.objects.filter(email='<EMAIL>').first()
            if not system_user:
                system_user = User.objects.create_superuser(
                    email='<EMAIL>',
                    name='System',
                    password=None,
                    role_id=1  # Assuming 1 is the ADMIN role
                )
            return (system_user, None)
        return None

class IsAuthenticatedWithToken(BasePermission):
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and hasattr(request, 'auth') and request.auth)

class IsAuthenticatedWithAPIKey(BasePermission):
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and not request.auth)

class JWTAuthenticationScheme(OpenApiAuthenticationExtension):
    target_class = 'users.authentication.JWTAuthentication'
    name = 'JWTAuth'

    def get_security_definition(self, auto_schema):
        return {
            'type': 'http',
            'scheme': 'bearer',
            'bearerFormat': 'JWT',
            'description': 'JWT Authorization header using the Bearer scheme. Example: "Authorization: Bearer {token}"',
        }

class APIKeyAuthenticationScheme(OpenApiAuthenticationExtension):
    target_class = 'users.authentication.APIKeyAuthentication'
    name = 'ApiKeyAuth'

    def get_security_definition(self, auto_schema):
        return {
            'type': 'apiKey',
            'in': 'header',
            'name': 'X-API-Key',
            'description': 'API Key authentication using the X-API-Key header.'
        } 