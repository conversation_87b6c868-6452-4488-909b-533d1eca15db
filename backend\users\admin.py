from django.contrib import admin
from .models import User, Role, VerificationCode

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ['role_id', 'role_name', 'description']
    search_fields = ['role_name', 'description']
    ordering = ['role_id']

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ['user_id', 'name', 'email', 'role', 'status', 'phone_number', 'is_agent', 'created_at']
    list_filter = ['role', 'status', 'created_at']
    search_fields = ['name', 'email', 'phone_number']
    readonly_fields = ['user_id', 'created_at', 'updated_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'email', 'phone_number', 'role', 'status')
        }),
        ('Agent Information', {
            'fields': ('agent_description', 'profile_image'),
            'classes': ('collapse',),
            'description': 'These fields are only relevant for agents'
        }),
        ('System Information', {
            'fields': ('user_id', 'created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    ordering = ['-created_at']

    def is_agent(self, obj):
        return obj.is_agent
    is_agent.boolean = True
    is_agent.short_description = 'Is Agent'

@admin.register(VerificationCode)
class VerificationCodeAdmin(admin.ModelAdmin):
    list_display = ['user', 'code', 'created_at', 'expires_at', 'is_valid']
    list_filter = ['created_at', 'expires_at']
    search_fields = ['user__name', 'user__email', 'code']
    readonly_fields = ['created_at']
    ordering = ['-created_at']

    def is_valid(self, obj):
        return obj.is_valid()
    is_valid.boolean = True
    is_valid.short_description = 'Is Valid'
