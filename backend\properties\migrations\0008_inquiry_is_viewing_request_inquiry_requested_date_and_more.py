# Generated by Django 5.1.7 on 2025-05-21 07:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0007_amenity_attraction_property_amenities_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='inquiry',
            name='is_viewing_request',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='inquiry',
            name='requested_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='inquiry',
            name='requested_time',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='inquiry',
            name='viewing_status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('CONFIRMED', 'Confirmed'), ('RESCHEDULED', 'Rescheduled'), ('CANCELLED', 'Cancelled'), ('COMPLETED', 'Completed')], default='PENDING', max_length=20),
        ),
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='properties.property')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('user', 'property')},
            },
        ),
    ]
