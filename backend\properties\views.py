from django.shortcuts import render
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiParameter
from .models import Property, Image, Inquiry, Transaction, Favorite, Amenity, Attraction
from .serializers import PropertySerializer, PropertyDetailSerializer, PropertyListSerializer, ImageSerializer, InquirySerializer, TransactionSerializer, FavoriteSerializer, FavoritePropertyListSerializer, AmenitySerializer, AttractionSerializer, MobilePropertyCreateSerializer
from .api.serializers import PropertyImageUploadSerializer
from users.permissions import IsAgent, IsClient
from django.utils import timezone
from django.db.models import Q
from rest_framework.pagination import PageNumberPagination
from django.core.cache import cache

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

# Utility function to get and cache the base64 of the primary image
def get_primary_image_base64(property_id):
    cache_key = f"primary_image_base64_{property_id}"
    base64_data = cache.get(cache_key)
    if base64_data is None:
        try:
            property_obj = Property.objects.get(pk=property_id)
            primary_image = property_obj.get_primary_image()
            base64_data = primary_image.image_data if primary_image else None
        except Property.DoesNotExist:
            base64_data = None
        cache.set(cache_key, base64_data, timeout=60*15)
    return base64_data

class PropertyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing property listings.
    
    Agents can create, update, and delete their own properties.
    All authenticated users can view properties.
    """
    queryset = Property.objects.all()
    serializer_class = PropertySerializer
    pagination_class = StandardResultsSetPagination
    
    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAgent]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return PropertyListSerializer
        elif self.action == 'retrieve':
            return PropertyDetailSerializer
        elif self.action == 'mobile_create':
            return MobilePropertyCreateSerializer
        return PropertySerializer
    
    def get_queryset(self):
        """
        Filter queryset to only show approved properties to regular users.
        Staff can see all properties.
        Agents can see their own properties regardless of status.
        """
        queryset = Property.objects.all()
        
        # If user is staff, show all properties
        if self.request.user.is_staff:
            return queryset
            
        # If user is an agent, show their own properties and approved properties
        if self.request.user.role.role_name == 'AGENT':
            return queryset.filter(
                Q(agent=self.request.user) | Q(status='APPROVED')
            )
            
        # For regular users, only show approved properties
        return queryset.filter(status='APPROVED')
    
    def retrieve(self, request, *args, **kwargs):
        property_id = kwargs.get('pk')
        cache_key = f"property_detail_{property_id}"
        data = cache.get(cache_key)
        if not data:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            data = serializer.data
            # Add primary image base64
            data['primary_image_base64'] = get_primary_image_base64(property_id)
            cache.set(cache_key, data, timeout=60*15)
        return Response(data)

    def list(self, request, *args, **kwargs):
        cache_key = f"property_list_{request.get_full_path()}"
        data = cache.get(cache_key)
        if data:
            return Response(data)
        response = super().list(request, *args, **kwargs)
        cache.set(cache_key, response.data, timeout=60*5)
        return response

    def perform_create(self, serializer):
        serializer.save(agent=self.request.user, status='PENDING')
        # Invalidate property list cache
        cache.clear()
    
    def perform_update(self, serializer):
        instance = self.get_object()
        serializer.save()
        # Invalidate detail and list cache
        cache.delete(f"property_detail_{instance.pk}")
        cache.clear()
    
    def perform_destroy(self, instance):
        pk = instance.pk
        super().perform_destroy(instance)
        cache.delete(f"property_detail_{pk}")
        cache.clear()
    
    @extend_schema(
        parameters=[
            OpenApiParameter(name='search', type=str, description='Search in title, description, and location'),
            OpenApiParameter(name='min_price', type=float, description='Minimum price filter'),
            OpenApiParameter(name='max_price', type=float, description='Maximum price filter'),
            OpenApiParameter(name='type', type=str, description='Property type filter'),
            OpenApiParameter(name='location', type=str, description='Location filter'),
            OpenApiParameter(name='bedrooms', type=int, description='Number of bedrooms filter'),
            OpenApiParameter(name='listing_type', type=str, description='Listing type filter (SALE/RENT)'),
            OpenApiParameter(name='is_featured', type=bool, description='Filter for featured properties'),
            OpenApiParameter(name='page', type=int, description='Page number'),
            OpenApiParameter(name='page_size', type=int, description='Number of items per page'),
        ]
    )
    def list(self, request, *args, **kwargs):
        """
        List all properties with search and filtering capabilities.
        Supports pagination with page and page_size parameters.
        """
        queryset = self.get_queryset()
        
        # Search functionality
        search_query = request.query_params.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(location__icontains=search_query)
            )
        
        # Price range filtering
        min_price = request.query_params.get('min_price')
        if min_price:
            try:
                queryset = queryset.filter(price__gte=float(min_price))
            except ValueError:
                pass
        
        max_price = request.query_params.get('max_price')
        if max_price:
            try:
                queryset = queryset.filter(price__lte=float(max_price))
            except ValueError:
                pass
        
        # Property type filtering
        property_type = request.query_params.get('type')
        if property_type:
            queryset = queryset.filter(type=property_type.upper())
        
        # Location filtering
        location = request.query_params.get('location')
        if location:
            queryset = queryset.filter(location__icontains=location)
        
        # Bedrooms filtering
        bedrooms = request.query_params.get('bedrooms')
        if bedrooms:
            try:
                queryset = queryset.filter(bedrooms=int(bedrooms))
            except ValueError:
                pass
        
        # Listing type filtering
        listing_type = request.query_params.get('listing_type')
        if listing_type:
            queryset = queryset.filter(listing_type=listing_type.upper())
        
        # Featured properties filtering
        is_featured = request.query_params.get('is_featured')
        if is_featured is not None:
            if is_featured.lower() in ['true', '1', 'yes']:
                queryset = queryset.filter(is_featured=True)
            elif is_featured.lower() in ['false', '0', 'no']:
                queryset = queryset.filter(is_featured=False)
        
        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        parameters=[
            OpenApiParameter(name='page', type=int, description='Page number'),
            OpenApiParameter(name='page_size', type=int, description='Number of items per page'),
        ]
    )
    @action(detail=False, methods=['get'])
    def images(self, request):
        """
        List all property images with their associated property IDs.
        Supports pagination with page and page_size parameters.
        """
        images = Image.objects.all()
        page = self.paginate_queryset(images)
        if page is not None:
            serializer = ImageSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = ImageSerializer(images, many=True)
        return Response(serializer.data)

    @extend_schema(
        parameters=[
            OpenApiParameter(name='page', type=int, description='Page number'),
            OpenApiParameter(name='page_size', type=int, description='Number of items per page'),
        ]
    )
    @action(detail=False, methods=['get'])
    def primary_images(self, request):
        """
        List all properties with their primary images.
        If a property has no primary image set, returns the first uploaded image.
        Supports pagination with page and page_size parameters.
        """
        properties = Property.objects.all()
        result = []
        
        for property in properties:
            primary_image = property.get_primary_image()
            if primary_image:
                result.append({
                    'property_id': property.property_id,
                    'image_url': primary_image.get_image_url(),
                    'caption': primary_image.caption,
                    'is_primary': primary_image.is_primary
                })
        
        page = self.paginate_queryset(result)
        if page is not None:
            return self.get_paginated_response(page)
        return Response(result)

    @extend_schema(
        parameters=[
            OpenApiParameter(name='search', type=str, description='Search in title, description, and location'),
            OpenApiParameter(name='status', type=str, description='Filter by property status (PENDING, APPROVED, REJECTED)'),
            OpenApiParameter(name='type', type=str, description='Property type filter (HOUSE, APARTMENT, CONDO, LAND, COMMERCIAL)'),
            OpenApiParameter(name='listing_type', type=str, description='Listing type filter (SALE/RENT)'),
            OpenApiParameter(name='min_price', type=float, description='Minimum price filter'),
            OpenApiParameter(name='max_price', type=float, description='Maximum price filter'),
            OpenApiParameter(name='bedrooms', type=int, description='Number of bedrooms filter'),
            OpenApiParameter(name='is_featured', type=bool, description='Filter for featured properties'),
            OpenApiParameter(name='page', type=int, description='Page number'),
            OpenApiParameter(name='page_size', type=int, description='Number of items per page'),
        ],
        description="Get properties created by the current agent with comprehensive filtering and pagination",
        responses={200: PropertyListSerializer(many=True)}
    )
    @action(detail=False, methods=['get'], permission_classes=[IsAgent])
    def my_properties(self, request):
        """
        List properties created by the current agent.

        Returns all properties where the current user is the agent,
        regardless of approval status. Supports comprehensive filtering
        and pagination similar to the main property list endpoint.
        """
        queryset = Property.objects.filter(agent=request.user)

        # Apply comprehensive filtering
        queryset = self._apply_property_filters(queryset, request)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PropertyListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = PropertyListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        parameters=[
            OpenApiParameter(name='agent_id', type=int, description='Agent user ID', required=True),
            OpenApiParameter(name='search', type=str, description='Search in title, description, and location'),
            OpenApiParameter(name='type', type=str, description='Property type filter (HOUSE, APARTMENT, CONDO, LAND, COMMERCIAL)'),
            OpenApiParameter(name='listing_type', type=str, description='Listing type filter (SALE/RENT)'),
            OpenApiParameter(name='min_price', type=float, description='Minimum price filter'),
            OpenApiParameter(name='max_price', type=float, description='Maximum price filter'),
            OpenApiParameter(name='bedrooms', type=int, description='Number of bedrooms filter'),
            OpenApiParameter(name='is_featured', type=bool, description='Filter for featured properties'),
            OpenApiParameter(name='page', type=int, description='Page number'),
            OpenApiParameter(name='page_size', type=int, description='Number of items per page'),
        ],
        description="Get properties created by a specific agent (only approved properties visible to non-agents)",
        responses={200: PropertyListSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def agent_properties(self, request):
        """
        List properties created by a specific agent.

        Returns approved properties for the specified agent. If the requesting user
        is the same agent, returns all properties regardless of status.
        """
        agent_id = request.query_params.get('agent_id')
        if not agent_id:
            return Response(
                {'error': 'agent_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            agent_id = int(agent_id)
        except ValueError:
            return Response(
                {'error': 'agent_id must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if agent exists and has AGENT role
        try:
            from users.models import User
            agent = User.objects.get(user_id=agent_id, role__role_name='AGENT')
        except User.DoesNotExist:
            return Response(
                {'error': 'Agent not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Base queryset
        queryset = Property.objects.filter(agent=agent)

        # If requesting user is not the same agent, only show approved properties
        if not (request.user.is_authenticated and request.user.user_id == agent_id):
            queryset = queryset.filter(status='APPROVED')

        # Apply comprehensive filtering (excluding status filter for non-owners)
        queryset = self._apply_property_filters(queryset, request, exclude_status=True)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PropertyListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = PropertyListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    def _apply_property_filters(self, queryset, request, exclude_status=False):
        """
        Apply comprehensive filtering to property queryset.

        Args:
            queryset: Base queryset to filter
            request: HTTP request object containing query parameters
            exclude_status: Whether to exclude status filtering (for public views)

        Returns:
            Filtered queryset
        """
        # Search functionality
        search_query = request.query_params.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(location__icontains=search_query)
            )

        # Status filtering (only for agent's own properties)
        if not exclude_status:
            status_filter = request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter.upper())

        # Price range filtering
        min_price = request.query_params.get('min_price')
        if min_price:
            try:
                queryset = queryset.filter(price__gte=float(min_price))
            except ValueError:
                pass

        max_price = request.query_params.get('max_price')
        if max_price:
            try:
                queryset = queryset.filter(price__lte=float(max_price))
            except ValueError:
                pass

        # Property type filtering
        property_type = request.query_params.get('type')
        if property_type:
            queryset = queryset.filter(type=property_type.upper())

        # Bedrooms filtering
        bedrooms = request.query_params.get('bedrooms')
        if bedrooms:
            try:
                queryset = queryset.filter(bedrooms=int(bedrooms))
            except ValueError:
                pass

        # Listing type filtering
        listing_type = request.query_params.get('listing_type')
        if listing_type:
            queryset = queryset.filter(listing_type=listing_type.upper())

        # Featured properties filtering
        is_featured = request.query_params.get('is_featured')
        if is_featured is not None:
            if is_featured.lower() in ['true', '1', 'yes']:
                queryset = queryset.filter(is_featured=True)
            elif is_featured.lower() in ['false', '0', 'no']:
                queryset = queryset.filter(is_featured=False)

        return queryset

    @extend_schema(
        request=MobilePropertyCreateSerializer,
        responses={201: PropertyDetailSerializer},
        description="Create a new property (mobile-optimized endpoint)"
    )
    @action(detail=False, methods=['post'], permission_classes=[IsAgent])
    def mobile_create(self, request):
        """
        Mobile-optimized property creation endpoint.

        Accepts property data with amenities and attractions,
        automatically sets the agent to the current user.
        """
        serializer = MobilePropertyCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Create the property
        property_obj = serializer.save(agent=request.user, status='PENDING')

        # Return detailed property data
        response_serializer = PropertyDetailSerializer(property_obj, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @extend_schema(
        description="Get property statistics for the current agent",
        responses={200: {"type": "object", "properties": {
            "total_properties": {"type": "integer"},
            "pending_properties": {"type": "integer"},
            "approved_properties": {"type": "integer"},
            "rejected_properties": {"type": "integer"},
            "featured_properties": {"type": "integer"}
        }}}
    )
    @action(detail=False, methods=['get'], permission_classes=[IsAgent])
    def stats(self, request):
        """
        Get property statistics for the current agent.

        Returns counts of properties by status and other metrics.
        """
        agent_properties = Property.objects.filter(agent=request.user)

        stats = {
            'total_properties': agent_properties.count(),
            'pending_properties': agent_properties.filter(status='PENDING').count(),
            'approved_properties': agent_properties.filter(status='APPROVED').count(),
            'rejected_properties': agent_properties.filter(status='REJECTED').count(),
            'featured_properties': agent_properties.filter(is_featured=True).count(),
        }

        return Response(stats)

class ImageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing property images.

    Only agents can manage images for their properties.
    Supports single and bulk image uploads.
    """
    queryset = Image.objects.all()
    serializer_class = ImageSerializer
    parser_classes = (MultiPartParser, FormParser)
    permission_classes = [IsAgent]
    schema = None  # Temporarily exclude from schema
    
    def get_queryset(self):
        return Image.objects.filter(property__agent=self.request.user)
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'bulk_upload':
            from .api.serializers import PropertyImageUploadSerializer
            return PropertyImageUploadSerializer
        return ImageSerializer
    
    @extend_schema(
        request={'multipart/form-data': ImageSerializer},
        responses={201: ImageSerializer}
    )
    def create(self, request, *args, **kwargs):
        """
        Upload a single image for a property.
        
        Required fields:
        - image: Image file
        - property: Property ID
        
        Optional fields:
        - caption: Image caption
        - is_primary: Boolean to mark as primary image
        """
        property_id = request.data.get('property')
        if not Property.objects.filter(property_id=property_id, agent=request.user).exists():
            return Response(
                {'error': 'You can only upload images to your own properties'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        image = serializer.save()
        
        return Response({
            'message': 'Image uploaded successfully',
            'image': ImageSerializer(image, context={'request': request}).data
        }, status=status.HTTP_201_CREATED)
    
    @extend_schema(
        request={'multipart/form-data': PropertyImageUploadSerializer},
        responses={201: {"type": "object", "properties": {
            "message": {"type": "string"},
            "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageSerializer"}}
        }}}
    )
    @action(detail=False, methods=['post'], url_path='bulk-upload')
    def bulk_upload(self, request):
        """
        Upload multiple images for a property in a single request.
        
        Required fields:
        - images: List of image files
        - property_id: Property ID
        
        Optional fields:
        - captions: List of captions for the images
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        property_obj = serializer.validated_data['property_id']
        images = serializer.validated_data['images']
        captions = serializer.validated_data.get('captions', [])
        
        created_images = []
        for i, image_file in enumerate(images):
            caption = captions[i] if i < len(captions) else ''
            
            image_obj = Image.objects.create(
                property=property_obj,
                image=image_file,
                caption=caption,
                is_primary=False
            )
            created_images.append(image_obj)
        
        # Set the first image as primary if no primary exists
        if created_images and not property_obj.images.filter(is_primary=True).exists():
            created_images[0].is_primary = True
            created_images[0].save()
        
        return Response({
            'message': f'{len(created_images)} images uploaded successfully',
            'images': ImageSerializer(created_images, many=True, context={'request': request}).data
        }, status=status.HTTP_201_CREATED)
    
    @extend_schema(
        parameters=[
            OpenApiParameter(name='property', type=int, description='Filter images by property ID'),
            OpenApiParameter(name='is_primary', type=bool, description='Filter by primary status'),
        ]
    )
    def list(self, request, *args, **kwargs):
        """
        List images with optional filtering.
        """
        queryset = self.get_queryset()
        
        property_id = request.query_params.get('property')
        is_primary = request.query_params.get('is_primary')
        
        if property_id:
            queryset = queryset.filter(property_id=property_id)
        if is_primary is not None:
            queryset = queryset.filter(is_primary=is_primary.lower() == 'true')
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """
        Set an image as the primary image for its property.
        """
        image = self.get_object()
        
        # Remove primary status from other images of the same property
        Image.objects.filter(property=image.property, is_primary=True).update(is_primary=False)
        
        # Set this image as primary
        image.is_primary = True
        image.save()
        
        return Response({
            'message': 'Image set as primary successfully',
            'image': ImageSerializer(image, context={'request': request}).data
        })
    
    @action(detail=True, methods=['post'])
    def remove_primary(self, request, pk=None):
        """
        Remove primary status from an image.
        """
        image = self.get_object()
        image.is_primary = False
        image.save()
        
        return Response({
            'message': 'Primary status removed successfully',
            'image': ImageSerializer(image, context={'request': request}).data
        })

class InquiryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing property inquiries.

    Clients can create inquiries and view their own inquiries.
    Agents can view and update inquiries for their properties.
    """
    queryset = Inquiry.objects.all()
    serializer_class = InquirySerializer
    schema = None  # Temporarily exclude from schema
    
    def get_permissions(self):
        if self.action == 'create':
            permission_classes = [IsClient]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        user = self.request.user
        if user.role.role_name == 'AGENT':
            return Inquiry.objects.filter(property__agent=user)
        elif user.role.role_name == 'CLIENT':
            return Inquiry.objects.filter(client=user)
        return Inquiry.objects.none()
    
    def perform_create(self, serializer):
        serializer.save(client=self.request.user)
    
    @extend_schema(
        parameters=[
            OpenApiParameter(name='status', type=str, description='Filter inquiries by status'),
            OpenApiParameter(name='property', type=int, description='Filter inquiries by property ID'),
        ]
    )
    def list(self, request, *args, **kwargs):
        """
        List inquiries with optional filtering.
        """
        queryset = self.get_queryset()
        
        status_filter = request.query_params.get('status')
        property_id = request.query_params.get('property')
        
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if property_id:
            queryset = queryset.filter(property_id=property_id)
            
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class TransactionViewSet(viewsets.ModelViewSet):
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    schema = None  # Temporarily exclude from schema

    def get_queryset(self):
        user = self.request.user
        if user.role.role_name == 'CLIENT':
            return Transaction.objects.filter(client=user)
        elif user.role.role_name == 'AGENT':
            return Transaction.objects.filter(property__agent=user)
        return Transaction.objects.all()

    def perform_create(self, serializer):
        serializer.save(transaction_date=timezone.now())

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        transaction = self.get_object()
        if transaction.status != 'PENDING':
            return Response(
                {'error': 'Only pending transactions can be completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        transaction.status = 'COMPLETED'
        transaction.save()
        return Response(TransactionSerializer(transaction).data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        transaction = self.get_object()
        if transaction.status not in ['PENDING', 'COMPLETED']:
            return Response(
                {'error': 'Only pending or completed transactions can be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        transaction.status = 'CANCELLED'
        transaction.save()
        return Response(TransactionSerializer(transaction).data)

class FavoriteViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user favorites.

    Users can add/remove properties to/from their favorites list.
    """
    queryset = Favorite.objects.all()  # Default queryset for schema generation
    serializer_class = FavoriteSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # Explicitly specify lookup field
    schema = None  # Temporarily exclude from schema


    def get_queryset(self):
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            return Favorite.objects.filter(user=self.request.user)
        return Favorite.objects.none()
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
    
    @action(detail=False, methods=['post'])
    def toggle(self, request):
        """Toggle a property as favorite/unfavorite"""
        property_id = request.data.get('property_id')
        if not property_id:
            return Response({"error": "Property ID is required"}, status=400)
            
        try:
            property = Property.objects.get(property_id=property_id)
            favorite, created = Favorite.objects.get_or_create(
                user=request.user,
                property=property
            )
            
            if not created:
                favorite.delete()
                return Response({"status": "removed"})
            return Response({"status": "added"})
        except Property.DoesNotExist:
            return Response({"error": "Property not found"}, status=404)

    @action(detail=False, methods=['get'], url_path='properties')
    def favorite_properties(self, request):
        """
        List the current user's favorite properties with only their primary images.
        """
        favorites = Favorite.objects.filter(user=request.user).select_related('property')
        properties = [fav.property for fav in favorites]
        page = self.paginate_queryset(properties)
        serializer = FavoritePropertyListSerializer(page, many=True) if page is not None else FavoritePropertyListSerializer(properties, many=True)
        if page is not None:
            return self.get_paginated_response(serializer.data)
        return Response(serializer.data)

class AmenityViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for listing amenities.

    Provides read-only access to amenities for property creation.
    All authenticated users can view amenities.
    """
    queryset = Amenity.objects.all()
    serializer_class = AmenitySerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None  # Disable pagination for amenities

    @extend_schema(
        description="List all available amenities for property creation",
        responses={200: AmenitySerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """
        List all available amenities.

        Returns all amenities that can be associated with properties.
        No pagination is applied as the list is typically small.
        """
        return super().list(request, *args, **kwargs)

class AttractionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for listing attractions.

    Provides read-only access to attractions for property creation.
    All authenticated users can view attractions.
    """
    queryset = Attraction.objects.all()
    serializer_class = AttractionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None  # Disable pagination for attractions

    @extend_schema(
        description="List all available attractions for property creation",
        responses={200: AttractionSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """
        List all available attractions.

        Returns all attractions that can be associated with properties.
        No pagination is applied as the list is typically small.
        """
        return super().list(request, *args, **kwargs)
