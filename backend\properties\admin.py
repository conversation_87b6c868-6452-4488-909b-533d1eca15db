from django.contrib import admin
from django.contrib import messages
from .models import Property, Image, Inquiry, Transaction, Amenity, Attraction
from django.core.mail import send_mail
from django.conf import settings

@admin.action(description="Approve selected properties")
def approve_properties(modeladmin, request, queryset):
    updated = queryset.update(status='APPROVED')
    
    # Send email notifications
    for property in queryset:
        if hasattr(settings, 'EMAIL_ENABLED') and settings.EMAIL_ENABLED:
            send_mail(
                'Your property listing has been approved',
                f'Your property listing "{property.title}" has been approved and is now visible to clients.',
                settings.DEFAULT_FROM_EMAIL,
                [property.agent.email],
                fail_silently=True,
            )
    
    modeladmin.message_user(
        request,
        f"{updated} properties were successfully approved.",
        messages.SUCCESS,
    )

@admin.action(description="Reject selected properties")
def reject_properties(modeladmin, request, queryset):
    updated = queryset.update(status='REJECTED')
    
    # Send email notifications
    for property in queryset:
        if hasattr(settings, 'EMAIL_ENABLED') and settings.EMAIL_ENABLED:
            send_mail(
                'Your property listing has been rejected',
                f'Your property listing "{property.title}" has been rejected. Please contact the administrator for more information.',
                settings.DEFAULT_FROM_EMAIL,
                [property.agent.email],
                fail_silently=True,
            )
    
    modeladmin.message_user(
        request,
        f"{updated} properties were successfully rejected.",
        messages.SUCCESS,
    )

@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = ('property_id', 'title', 'type', 'price', 'location', 'agent', 'status', 'is_featured', 'created_at')
    list_filter = ('type', 'agent', 'status', 'is_featured', 'created_at')
    search_fields = ('title', 'description', 'location')
    readonly_fields = ('created_at', 'updated_at')
    actions = [approve_properties, reject_properties]
    list_editable = ('is_featured',)

@admin.register(Image)
class ImageAdmin(admin.ModelAdmin):
    list_display = ('image_id', 'property', 'is_primary', 'uploaded_at')
    list_filter = ('is_primary', 'uploaded_at')
    search_fields = ('property__title', 'caption')

@admin.register(Inquiry)
class InquiryAdmin(admin.ModelAdmin):
    list_display = ('inquiry_id', 'property', 'client', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('property__title', 'client__name', 'message')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction_id', 'client', 'property', 'amount', 'status', 'transaction_date', 'created_at')
    list_filter = ('status', 'transaction_date', 'created_at')
    search_fields = ('client__name', 'property__title')
    readonly_fields = ('created_at',)
    date_hierarchy = 'transaction_date'

@admin.register(Amenity)
class AmenityAdmin(admin.ModelAdmin):
    list_display = ('name', 'icon')
    search_fields = ('name',)

@admin.register(Attraction)
class AttractionAdmin(admin.ModelAdmin):
    list_display = ('name', 'distance', 'icon')
    search_fields = ('name',)
