from rest_framework import serializers
from .models import User, Role
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from drf_spectacular.utils import extend_schema_field
from typing import Optional

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['role_id', 'role_name', 'description']

class UserSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    role_id = serializers.PrimaryKeyRelatedField(
        source='role',
        queryset=Role.objects.all(),
        write_only=True
    )
    profile_image_url = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['user_id', 'name', 'email', 'phone_number', 'role', 'role_id', 'agent_description', 'profile_image', 'profile_image_url', 'created_at', 'updated_at']
        read_only_fields = ['user_id', 'created_at', 'updated_at']

    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_profile_image_url(self, obj) -> Optional[str]:
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

class AgentCreationSerializer(serializers.ModelSerializer):
    """
    Serializer for creating agents via API
    """
    password = serializers.CharField(write_only=True, min_length=8, help_text="Password must be at least 8 characters")
    confirm_password = serializers.CharField(write_only=True, help_text="Confirm password")
    role_id = serializers.PrimaryKeyRelatedField(
        queryset=Role.objects.filter(role_name='AGENT'),
        source='role',
        write_only=True,
        help_text="Role ID (must be AGENT role)"
    )
    profile_image_url = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = [
            'user_id', 'name', 'email', 'phone_number', 'password', 'confirm_password',
            'agent_description', 'profile_image', 'profile_image_url', 'role_id',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['user_id', 'created_at', 'updated_at']
        extra_kwargs = {
            'name': {'help_text': 'Full name of the agent'},
            'email': {'help_text': 'Email address (must be unique)'},
            'phone_number': {'help_text': 'Phone number (optional)'},
            'agent_description': {'help_text': 'Agent biography or description'},
            'profile_image': {'help_text': 'Profile image file (optional)'},
        }

    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_profile_image_url(self, obj) -> Optional[str]:
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

    def validate_email(self, value):
        """Validate that email is unique"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_password(self, value):
        """Validate password strength"""
        if len(value) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long.")
        return value

    def validate(self, data):
        """Validate password confirmation and role"""
        password = data.get('password')
        confirm_password = data.get('confirm_password')
        
        if password and confirm_password and password != confirm_password:
            raise serializers.ValidationError("Passwords don't match.")
        
        # Ensure role is AGENT
        role = data.get('role')
        if role and role.role_name != 'AGENT':
            raise serializers.ValidationError("Only AGENT role is allowed for agent creation.")
        
        return data

    def create(self, validated_data):
        """Create a new agent user"""
        # Remove confirm_password from validated data
        validated_data.pop('confirm_password', None)
        
        # Set username to email if not provided
        if 'username' not in validated_data:
            validated_data['username'] = validated_data['email']
        
        # Create the user
        user = User.objects.create_user(**validated_data)
        
        return user

class AgentProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for agent profile management
    """
    profile_image_url = serializers.SerializerMethodField()
    role_name = serializers.CharField(source='role.role_name', read_only=True)

    class Meta:
        model = User
        fields = ['user_id', 'name', 'email', 'phone_number', 'agent_description', 'profile_image', 'profile_image_url', 'role_name', 'created_at', 'updated_at']
        read_only_fields = ['user_id', 'name', 'email', 'role_name', 'created_at', 'updated_at']

    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_profile_image_url(self, obj) -> Optional[str]:
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

    def validate(self, data):
        # Ensure only agents can update their profile
        user = self.context['request'].user
        if not user.is_agent:
            raise serializers.ValidationError("Only agents can update their profile")
        return data

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        user = self.user
        
        # Add user details to the response
        data['user'] = {
            'user_id': user.user_id,
            'name': user.name,
            'email': user.email,
            'phone_number': user.phone_number,
            'agent_description': user.agent_description,
            'profile_image_url': user.profile_image.url if user.profile_image else None,
            'role': {
                'role_id': user.role.role_id,
                'role_name': user.role.role_name,
                'description': user.role.description
            }
        }
        
        return data 
