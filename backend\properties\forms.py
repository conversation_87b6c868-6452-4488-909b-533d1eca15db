from django import forms
from .models import Property, Image, Inquiry
from users.models import User

class PropertyForm(forms.ModelForm):
    agent = forms.ModelChoiceField(
        queryset=User.objects.filter(role__role_name='AGENT'),
        required=True,
        label='Agent'
    )

    class Meta:
        model = Property
        fields = [
            'title', 'description', 'price', 'location',
            'size', 'type', 'agent', 'bedrooms', 'listing_type',
            'amenities', 'attractions', 'is_featured'  
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'price': forms.NumberInput(attrs={'class': 'form-control'}),
            'location': forms.TextInput(attrs={'class': 'form-control'}),
            'size': forms.NumberInput(attrs={'class': 'form-control'}),
            'type': forms.Select(attrs={'class': 'form-control'}),
            'agent': forms.Select(attrs={'class': 'form-control'}),
            'amenities': forms.CheckboxSelectMultiple(),
            'attractions': forms.CheckboxSelectMultiple(),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'})  
        }

class MultipleFileInput(forms.FileInput):
    allow_multiple_selected = True

class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result

class PropertyImageForm(forms.ModelForm):
    image = MultipleFileField(
        label='Property Images',
        widget=MultipleFileInput(attrs={'class': 'form-control'})
    )
    
    class Meta:
        model = Image
        fields = ['image', 'caption', 'is_primary']
        widgets = {
            'caption': forms.TextInput(attrs={'class': 'form-control'}),
            'is_primary': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class PropertySearchForm(forms.Form):
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search by title or location'})
    )
    min_price = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Min Price'})
    )
    max_price = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Max Price'})
    )
    property_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + Property.PROPERTY_TYPES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    location = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Location'})
    )
    listing_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All'), ('SALE', 'For Sale'), ('RENT', 'For Rent')],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    bedrooms = forms.ChoiceField(
        required=False,
        choices=[('', 'Any'), ('1', '1+'), ('2', '2+'), ('3', '3+'), ('4', '4+'), ('5', '5+')],
        widget=forms.Select(attrs={'class': 'form-control'})
    )

class InquiryForm(forms.ModelForm):
    class Meta:
        model = Inquiry
        fields = ['message']
        widgets = {
            'message': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Enter your message or questions about this property'}),
        }

class ViewingRequestForm(forms.ModelForm):
    class Meta:
        model = Inquiry
        fields = ['message', 'requested_date', 'requested_time']
        widgets = {
            'requested_date': forms.DateInput(attrs={'type': 'date'}),
            'requested_time': forms.TimeInput(attrs={'type': 'time'}),
            'message': forms.Textarea(attrs={'rows': 3, 'placeholder': 'Additional information about your visit'})
        }

