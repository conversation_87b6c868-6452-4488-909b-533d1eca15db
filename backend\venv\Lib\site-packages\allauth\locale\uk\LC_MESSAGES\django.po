# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-03 04:26-0500\n"
"PO-Revision-Date: 2020-10-15 19:53+0200\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#: account/adapter.py:51
msgid "Username can not be used. Please use other username."
msgstr ""
"Ім'я користувача не може бути використаним. Будь ласка, оберіть інше ім'я "
"користувача."

#: account/adapter.py:57
msgid "Too many failed login attempts. Try again later."
msgstr "Занадто багато спроб входу в систему, спробуйте пізніше."

#: account/adapter.py:59
msgid "A user is already registered with this email address."
msgstr "Користувач з такою e-mail адресою уже зареєстрований."

#: account/adapter.py:60
msgid "Incorrect password."
msgstr "Неправильний пароль."

#: account/adapter.py:340
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Пароль повинен містити мінімум {0} символів."

#: account/apps.py:9
msgid "Accounts"
msgstr "Акаунти"

#: account/forms.py:61 account/forms.py:445
msgid "You must type the same password each time."
msgstr "Ви повинні вводити однаковий пароль кожного разу."

#: account/forms.py:93 account/forms.py:408 account/forms.py:547
#: account/forms.py:685
msgid "Password"
msgstr "Пароль"

#: account/forms.py:94
msgid "Remember Me"
msgstr "Запам'ятати мене"

#: account/forms.py:98
msgid "This account is currently inactive."
msgstr "Даний акаунт є неактивним."

#: account/forms.py:100
msgid "The email address and/or password you specified are not correct."
msgstr "Введена e-mail адреса і/або пароль є некоректними."

#: account/forms.py:103
msgid "The username and/or password you specified are not correct."
msgstr "Введене ім'я користувача і/або пароль є некоректними."

#: account/forms.py:114 account/forms.py:283 account/forms.py:472
#: account/forms.py:567
msgid "Email address"
msgstr "E-mail адреса"

#: account/forms.py:118 account/forms.py:321 account/forms.py:469
#: account/forms.py:562
msgid "Email"
msgstr "E-mail"

#: account/forms.py:121 account/forms.py:124 account/forms.py:273
#: account/forms.py:276
msgid "Username"
msgstr "Ім'я користувача"

#: account/forms.py:134
msgid "Username or email"
msgstr "Ім'я користувача або e-mail"

#: account/forms.py:137
msgctxt "field label"
msgid "Login"
msgstr "Увійти"

#: account/forms.py:148
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Забули пароль?"

#: account/forms.py:312
msgid "Email (again)"
msgstr "E-mail (ще раз)"

#: account/forms.py:316
msgid "Email address confirmation"
msgstr "e-mail адреса підтвердження"

#: account/forms.py:324
msgid "Email (optional)"
msgstr "E-mail (необов'язковий)"

#: account/forms.py:379
msgid "You must type the same email each time."
msgstr "Ви повинні вводити однакову e-mail адресу кожного разу."

#: account/forms.py:414 account/forms.py:550
msgid "Password (again)"
msgstr "Пароль (ще раз)"

#: account/forms.py:484
msgid "This email address is already associated with this account."
msgstr "Вказаний e-mail уже прикріплений до цього акаунту."

#: account/forms.py:486
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Ви не можете додати більше %d адрес електронної пошти."

#: account/forms.py:524
msgid "Current Password"
msgstr "Поточний пароль"

#: account/forms.py:527 account/forms.py:634
msgid "New Password"
msgstr "Новий пароль"

#: account/forms.py:530 account/forms.py:635
msgid "New Password (again)"
msgstr "Новий пароль (ще раз)"

#: account/forms.py:538
msgid "Please type your current password."
msgstr "Будь ласка, вкажіть Ваш поточний пароль."

#: account/forms.py:579
msgid "The email address is not assigned to any user account"
msgstr "Немає користувача з такою e-mail адресою."

#: account/forms.py:655
msgid "The password reset token was invalid."
msgstr "Токен відновлення паролю був невірним."

#: account/models.py:21
msgid "user"
msgstr "користувач"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-mail адреса"

#: account/models.py:28
msgid "verified"
msgstr "підтверджено"

#: account/models.py:29
msgid "primary"
msgstr "основний"

#: account/models.py:35
msgid "email addresses"
msgstr "e-mail адреса"

#: account/models.py:141
msgid "created"
msgstr "створено"

#: account/models.py:142
msgid "sent"
msgstr "відправлено"

#: account/models.py:143 socialaccount/models.py:65
msgid "key"
msgstr "ключ"

#: account/models.py:148
msgid "email confirmation"
msgstr "e-mail підтвердження"

#: account/models.py:149
msgid "email confirmations"
msgstr "e-mail підтвердження"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:15 mfa/forms.py:17
msgid "Code"
msgstr ""

#: mfa/forms.py:48
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:32
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Обліковий запис з такою адресою вже існує. Будь ласка, спочатку увійдіть до "
"цього акаунта, а потім підключіть ваш акаунт %s."

#: socialaccount/adapter.py:138
msgid "Your account has no password set up."
msgstr "Ваш акаунт не має встановленого паролю."

#: socialaccount/adapter.py:145
msgid "Your account has no verified email address."
msgstr "Немає підтвердження по e-mail для Вашого акаунту."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Соціальні акаунти"

#: socialaccount/models.py:39 socialaccount/models.py:93
msgid "provider"
msgstr "постачальник"

#: socialaccount/models.py:48
msgid "provider ID"
msgstr "постачальник ID"

#: socialaccount/models.py:52
msgid "name"
msgstr "Ім'я"

#: socialaccount/models.py:54
msgid "client id"
msgstr "ідентифікатор клієнта"

#: socialaccount/models.py:56
msgid "App ID, or consumer key"
msgstr "ідентифікатор додатку або ключ користувача"

#: socialaccount/models.py:59
msgid "secret key"
msgstr "секретний ключ"

#: socialaccount/models.py:62
msgid "API secret, client secret, or consumer secret"
msgstr ""
"секретний ключ додатку, секретний ключ клієнта або секретний ключ користувача"

#: socialaccount/models.py:65
msgid "Key"
msgstr "Ключ"

#: socialaccount/models.py:77
msgid "social application"
msgstr "соціальний додаток"

#: socialaccount/models.py:78
msgid "social applications"
msgstr "соціальні додатки"

#: socialaccount/models.py:113
msgid "uid"
msgstr "ID користувача"

#: socialaccount/models.py:115
msgid "last login"
msgstr "дата останнього входу"

#: socialaccount/models.py:116
msgid "date joined"
msgstr "дата реєстрації"

#: socialaccount/models.py:117
msgid "extra data"
msgstr "додаткові дані"

#: socialaccount/models.py:121
msgid "social account"
msgstr "аккаунт соціальної мережі"

#: socialaccount/models.py:122
msgid "social accounts"
msgstr "акаунти соціальних мереж"

#: socialaccount/models.py:156
msgid "token"
msgstr "токен"

#: socialaccount/models.py:157
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) або access token (OAuth2)"

#: socialaccount/models.py:161
msgid "token secret"
msgstr "секретний токен"

#: socialaccount/models.py:162
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) або refresh token (OAuth2)"

#: socialaccount/models.py:165
msgid "expires at"
msgstr "закінчується"

#: socialaccount/models.py:170
msgid "social application token"
msgstr "токен соціального додатку"

#: socialaccount/models.py:171
msgid "social application tokens"
msgstr "токени соціальних додатків"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Невірні дані профілю"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Невірна відповідь під час отримання запиту від \"%s\". Відповідь була: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Невірна відповідь під час отримання токену доступу від \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Немає збереженого ключа запиту для \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Токен доступу не збережений для \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Немає токену доступу для приватних ресурсів від \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Невірна відповідь під час отримання запиту від \"%s\""

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Акаунт неактивний"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Даний акаунт неактивний"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mail адреса"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "Вказаний e-mail уже прикріплений до цього акаунту"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Підтверджено"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Непідтверджено"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Основний"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Зробити основним"

#: templates/account/email.html:45 templates/account/email_change.html:29
msgid "Re-send Verification"
msgstr "Надіслати підтвердження ще раз"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Видалити"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Додати e-mail адресу"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Додати e-mail"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Ви дійсно бажаєте видалити дану e-mail адресу?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Вітання від %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Дякуємо Вам, що користуєтесь %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Ви отримали дане повідомлення тому, що користувач %(user_display)s вказав "
"вашу адресу електронну пошту для реєстрації акаунта на %(site_domain)s.\n"
"\n"
"Для підтвердження, що все правильно, будь ласка, перейдіть за посиланням "
"%(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Будь ласка, підтвердіть Вашу e-mail адресу"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Ви отримали дане повідомлення, тому що Ви або хтось інший зробили запит на "
"відновлення паролю для Вашого акаунту користувача на сайті %(site_domain)s.\n"
"Дане повідомлення можна проігнорувати, якщо Ви не робили такого запиту. "
"Перейдіть за посиланням для відновлення паролю."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "На випадок, якщо Ви забули Ваше ім'я користувача %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail для відновлення паролю"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Ви отримали цей лист, тому що ви або хтось інший надіслав запит на "
"отримання\n"
"паролю до вашого облікового запису. Однак у нас немає жодних записів про "
"користувача\n"
"з адресою %(email)s у нашій базі даних.\n"
"\n"
"Цей лист можна безпечно ігнорувати, якщо ви не надсилали запит на зміну "
"пароля.\n"
"\n"
"Якщо це були ви, ви можете зареєструватися за посиланням нижче."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-mail адреса"

#: templates/account/email_change.html:14
msgid "The following email address is associated with your account:"
msgstr "Вказаний e-mail уже прикріплений до цього акаунту:"

#: templates/account/email_change.html:19
msgid "Your email address is still pending verification:"
msgstr "Ваша e-mail адреса все ще очікує на перевірку:"

#: templates/account/email_change.html:38
msgid "Change Email Address"
msgstr "Змінити e-mail адресу"

#: templates/account/email_change.html:49
#: templates/allauth/layouts/base.html:29
msgid "Change Email"
msgstr "Змінити e-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Підтвердити e-mail адресу"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Будь ласка, підтвердіть, що <a href=\"mailto:%(email)s\">%(email)s</a> це e-"
"mail адреса для користувача %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:28
msgid "Confirm"
msgstr "Підтвердити"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Не вдалося підтвердити %(email)s, оскільки він вже підтверджений іншим "
"акаунтом."

#: templates/account/email_confirm.html:35
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Термін дії посилання для підтвердження електронної пошти закінчився або воно "
"недійсне. Будь ласка, <a href=\"%(email_url)s\">надішліть новий запит на "
"підтвердження електронної пошти</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Увійти"

#: templates/account/login.html:12
#, fuzzy, python-format
#| msgid ""
#| "If you have not created an account yet, then please\n"
#| "<a href=\"%(signup_url)s\">sign up</a> first."
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Якщо Ви ще не зареєструвались, будь ласка\n"
"<a href=\"%(signup_url)s\">зареєструйтесь</a>."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
msgid "Sign Out"
msgstr "Вийти"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Ви впевнені, що бажаєте вийти?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Ви можете видалити Вашу основну e-mail адресу (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "E-mail підтвердження надіслано на %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "E-mail %(email)s підтверджено. "

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mail %(email)s видалено."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Успішно увійшли як %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Ви вийшли."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Пароль успішно змінено."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Пароль успішно введено."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Основну e-mail адресу введено."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ваша основна e-mail адреса повинна бути підтверджена."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Змінити пароль"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Забули пароль?"

#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Відновити пароль"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Забули пароль? Введіть Вашу e-mail адресу у поле і ми надішлемо Вам e-mail, "
"що дозволить відновити пароль."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Відновити мій пароль"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Будь ласка, зв'яжіться з нами, якщо у Вас виникли проблеми по відновленню "
"паролю."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Ми надіслали вам електронного листа. Якщо ви його не отримали, перевірте, "
"будь ласка, папку \"Спам\". В іншому випадку зв'яжіться з нами, якщо ви не "
"отримаєте його протягом декількох хвилин."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Поганий токен"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Посилання для відновлення паролю некоректне, можливо через те, що посилання "
"уже використане.  Будь ласка, зробіть запит на <a href=\"%(passwd_reset_url)s"
"\">нове відновлення паролю</a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Ваш пароль змінено."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Введіть пароль"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
msgid "Confirm Access"
msgstr "Підтвердіть доступ"

#: templates/account/reauthenticate.html:12
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""
"Для забезпечення безпеки вашого облікового запису, будь ласка, введіть "
"пароль:"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Зареєструватись"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Зареєструватись"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Уже зареєстрованні? Будь ласка, <a href=\"%(login_url)s\">увійдіть</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Реєстрація закрита"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Перепрошуємо, але реєстрацію закрито."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Зауважте"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "Ви уже увійшли як %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Попередження:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"На даний момент у Вас немає збереженої e-mail адреси. Рекомендуємо додати e-"
"mail адресу, для того, щоб отримувати сповіщення, оновлювати паролі та інше."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Підтвердіть Вашу e-mail адресу"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Ми надіслали Вам e-mail для підтвердження. Перейдіть за посиланням для "
"звершення процесу реєстрації. Будь ласка, зв'яжіться з нами, якщо Ви не "
"отримаєте повідомлення впродовж декількох хвилин."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Дана частина сайту вимагає підтвердження e-mail адреси. Для цього нам "
"потрібно, щоб ви підтвердили право власності на вашу адресу електронної "
"пошти. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Ми надіслали Вам e-mail для підтвердження.\n"
"Будь ласка, перейдіть за посилання вказаним у e-mail повідомленні. Якщо ви "
"не бачите листа з підтвердженням в основній поштовій скриньці, перевірте "
"папку \"Спам\". Будь ласка,\n"
"зв'яжіться з нами, якщо Ви не отримаєте повідомлення впродовж декількох "
"хвилин."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Зауважте:</strong> Ви все ще можете <a href=\"%(email_url)s"
"\">змінити Вашу e-mail адресу</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:13 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:17
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:19
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:31 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:39 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:44 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: templates/mfa/index.html:47
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:56
msgid "View"
msgstr ""

#: templates/mfa/index.html:62
msgid "Download"
msgstr ""

#: templates/mfa/index.html:70 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Секрет аутентифікатора"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Social Network Login Failure"
msgstr "Вхід за допомогою соціальних мереж неуспішний."

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Виникла помилка під час входу за допомогою Вашого акаунту у соц. мережах."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "З'єднання акаунта"

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Ви можете увійти використовуючи будь-який із зовнішніх акаунтів:"

#: templates/socialaccount/connections.html:45
msgid ""
"You currently have no social network accounts connected to this account."
msgstr ""
"На даний момент Ви не маєте жодного акаунту із соц. мереж приєднаного до "
"даного акаунту."

#: templates/socialaccount/connections.html:48
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Додати зовнішній акаунт"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Вхід відмінено"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Ви відмінили вхід на наш сайт, використовуючи один з Ваших існуючих "
"акаунтів.  Якщо це сталось помилково, будь ласка, <a href=\"%(login_url)s"
"\">увійдіть</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Акаунт із соц. мереж було приєднано."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Акаунт із соц. мереж уже приєднано до іншого акаунту."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Акаунт із соц. мереж було від'єднано."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Ви використовуєте Ваш %(provider_name)s акаунт для авторизації на\n"
"%(site_name)s. Для завершення, будь ласка, заповніть наступну форму:"

#: templates/socialaccount/snippets/login.html:8
msgid "Or use a third-party"
msgstr ""

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a href=\"%(signup_url)s"
#~ "\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Будь ласка, увійдіть із одним \n"
#~ "із Ваших існуючих зовнішніх акаунтів accounts. Або, <a href="
#~ "\"%(signup_url)s\">Зареєструйте</a>\n"
#~ " %(site_name)s акаунт і Увійдіть:"

#~ msgid "or"
#~ msgstr "або"

#~ msgid "change password"
#~ msgstr "змінити пароль"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID вхід"
