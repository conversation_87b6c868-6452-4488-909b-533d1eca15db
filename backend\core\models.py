from django.db import models
from django.conf import settings

class EmailNotification(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    )
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    email_type = models.CharField(max_length=50)  # verification, welcome, password_reset, etc.
    subject = models.CharField(max_length=255)
    message = models.TextField()
    html_message = models.TextField(blank=True, null=True)  # Add this field
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    attempts = models.IntegerField(default=0)
    error_message = models.TextField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.email_type} for {self.user.email} ({self.status})"
