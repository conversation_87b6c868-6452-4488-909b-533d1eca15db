from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import PropertyViewSet, ImageViewSet, InquiryViewSet, TransactionViewSet, FavoriteViewSet, AmenityViewSet, AttractionViewSet

router = DefaultRouter()
router.register(r'properties', PropertyViewSet)
router.register(r'images', ImageViewSet)
router.register(r'inquiries', InquiryViewSet)
router.register(r'transactions', TransactionViewSet)
router.register(r'favorites', FavoriteViewSet, basename='favorite')
router.register(r'amenities', AmenityViewSet)
router.register(r'attractions', AttractionViewSet)

urlpatterns = [
    path('', include(router.urls)),
] 