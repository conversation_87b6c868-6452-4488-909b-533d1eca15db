from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import PropertyViewSet, ImageViewSet, InquiryViewSet, TransactionViewSet, FavoriteViewSet

router = DefaultRouter()
router.register(r'properties', PropertyViewSet)
router.register(r'images', ImageViewSet)
router.register(r'inquiries', InquiryViewSet)
router.register(r'transactions', TransactionViewSet)
router.register(r'favorites', FavoriteViewSet, basename='favorite')

urlpatterns = [
    path('', include(router.urls)),
] 