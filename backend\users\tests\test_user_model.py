from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db import utils as db_utils
from users.models import Role
import uuid

User = get_user_model()

class UserModelTest(TestCase):
    def setUp(self):
        self.role = Role.objects.create(role_name='CLIENT')
        # Use unique email for the base user
        self.user_data = {
            'username': f'test_{uuid.uuid4().hex[:8]}@example.com',
            'name': 'Test User',
            'email': f'test_{uuid.uuid4().hex[:8]}@example.com',
            'password': 'testpass123',
            'role': self.role,
            'phone_number': '1234567890'
        }
        self.user = User.objects.create_user(**self.user_data)

    def test_create_user_with_email(self):
        """Test creating a user with an email works"""
        # Use unique email for this test
        unique_email = f'another_{uuid.uuid4().hex[:8]}@example.com'
        user = User.objects.create_user(
            username=unique_email,
            email=unique_email,
            password='testpass123',
            role=self.role
        )
        
        self.assertEqual(user.email, unique_email)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        
    def test_create_superuser(self):
        """Test creating a superuser"""
        # Use unique email for this test
        unique_email = f'admin_{uuid.uuid4().hex[:8]}@example.com'
        admin_user = User.objects.create_superuser(
            username=unique_email,
            email=unique_email,
            password='testpass123',
            role=self.role
        )
        
        self.assertEqual(admin_user.email, unique_email)
        self.assertTrue(admin_user.is_active)
        self.assertTrue(admin_user.is_staff)
        self.assertTrue(admin_user.is_superuser)
        
    def test_new_user_email_normalized(self):
        """Test email is normalized for new users"""
        # Use unique email for this test
        unique_email = f'test_{uuid.uuid4().hex[:8]}@EXAMPLE.com'
        user = User.objects.create_user(
            username=unique_email,
            email=unique_email,
            password='testpass123',
            role=self.role
        )
        
        self.assertEqual(user.email, unique_email.lower())
        
    def test_new_user_without_email_raises_error(self):
        """Test that creating user without email raises error"""
        with self.assertRaises(ValueError):
            User.objects.create_user(
                username='',
                email='',
                password='testpass123',
                role=self.role
            )
            
    def test_new_user_without_role_raises_error(self):
        """Test that creating user without role raises error"""
        # The database enforces the role constraint with a NOT NULL constraint
        # So we should expect a database error, not a ValueError
        with self.assertRaises(db_utils.IntegrityError):
            User.objects.create_user(
                username=f'notrole_{uuid.uuid4().hex[:8]}@example.com',
                email=f'notrole_{uuid.uuid4().hex[:8]}@example.com',
                password='testpass123',
                role=None
            )



