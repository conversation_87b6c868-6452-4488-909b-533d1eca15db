# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("socialaccount", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="socialaccount",
            name="uid",
            field=models.Char<PERSON>ield(
                max_length=getattr(settings, "SOCIALACCOUNT_UID_MAX_LENGTH", 191),
                verbose_name="uid",
            ),
        ),
        migrations.AlterField(
            model_name="socialapp",
            name="client_id",
            field=models.Char<PERSON>ield(
                help_text="App ID, or consumer key",
                max_length=191,
                verbose_name="client id",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="socialapp",
            name="key",
            field=models.Char<PERSON><PERSON>(
                help_text="Key", max_length=191, verbose_name="key", blank=True
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="socialapp",
            name="secret",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                help_text="API secret, client secret, or consumer secret",
                max_length=191,
                verbose_name="secret key",
                blank=True,
            ),
        ),
    ]
