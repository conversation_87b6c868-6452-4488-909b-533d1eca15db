# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-03 04:26-0500\n"
"PO-Revision-Date: 2023-11-03 10:25+0100\n"
"Last-Translator: pennersr <<EMAIL>>\n"
"Language-Team: Dutch (http://www.transifex.com/projects/p/django-allauth/"
"language/nl/)\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: account/adapter.py:51
msgid "Username can not be used. Please use other username."
msgstr "Deze gebruikersnaam mag je niet gebruiken, kies een andere."

#: account/adapter.py:57
msgid "Too many failed login attempts. Try again later."
msgstr "Te veel inlogpogingen. Probeer het later nogmaals."

#: account/adapter.py:59
msgid "A user is already registered with this email address."
msgstr "Er is al een gebruiker geregistreerd met dit e-mailadres."

#: account/adapter.py:60
msgid "Incorrect password."
msgstr "Ongeldig wachtwoord."

#: account/adapter.py:340
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Het wachtwoord moet minimaal {0} tekens bevatten."

#: account/apps.py:9
msgid "Accounts"
msgstr "Accounts"

#: account/forms.py:61 account/forms.py:445
msgid "You must type the same password each time."
msgstr "Je moet hetzelfde wachtwoord twee keer intoetsen."

#: account/forms.py:93 account/forms.py:408 account/forms.py:547
#: account/forms.py:685
msgid "Password"
msgstr "Wachtwoord"

#: account/forms.py:94
msgid "Remember Me"
msgstr "Onthouden"

#: account/forms.py:98
msgid "This account is currently inactive."
msgstr "Dit account is niet actief"

#: account/forms.py:100
msgid "The email address and/or password you specified are not correct."
msgstr "Je e-mailadres en/of wachtwoord zijn incorrect."

#: account/forms.py:103
msgid "The username and/or password you specified are not correct."
msgstr "Je gebruikersnaam en/of wachtwoord zijn incorrect."

#: account/forms.py:114 account/forms.py:283 account/forms.py:472
#: account/forms.py:567
msgid "Email address"
msgstr "E-mailadres"

#: account/forms.py:118 account/forms.py:321 account/forms.py:469
#: account/forms.py:562
msgid "Email"
msgstr "E-mail"

#: account/forms.py:121 account/forms.py:124 account/forms.py:273
#: account/forms.py:276
msgid "Username"
msgstr "Gebruikersnaam"

#: account/forms.py:134
msgid "Username or email"
msgstr "Gebruikersnaam of e-mail"

#: account/forms.py:137
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:148
msgid "Forgot your password?"
msgstr "Wachtwoord vergeten?"

#: account/forms.py:312
msgid "Email (again)"
msgstr "E-mail (bevestigen)"

#: account/forms.py:316
msgid "Email address confirmation"
msgstr "Bevestig e-mailadres"

#: account/forms.py:324
msgid "Email (optional)"
msgstr "E-mail (optioneel)"

#: account/forms.py:379
msgid "You must type the same email each time."
msgstr "Je moet hetzelfde e-mailadres twee keer intoetsen."

#: account/forms.py:414 account/forms.py:550
msgid "Password (again)"
msgstr "Wachtwoord (bevestigen)"

#: account/forms.py:484
msgid "This email address is already associated with this account."
msgstr "Dit e-mailadres is al geassocieerd met dit account."

#: account/forms.py:486
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Je kunt niet meer dan %d e-mailadressen toevoegen."

#: account/forms.py:524
msgid "Current Password"
msgstr "Huidig wachtwoord"

#: account/forms.py:527 account/forms.py:634
msgid "New Password"
msgstr "Nieuw wachtwoord"

#: account/forms.py:530 account/forms.py:635
msgid "New Password (again)"
msgstr "Nieuw wachtwoord (bevestigen)"

#: account/forms.py:538
msgid "Please type your current password."
msgstr "Geef je huidige wachtwoord op."

#: account/forms.py:579
msgid "The email address is not assigned to any user account"
msgstr "Dit e-mailadres is niet bij ons bekend"

#: account/forms.py:655
msgid "The password reset token was invalid."
msgstr "De wachtwoordherstel-sleutel is niet geldig."

#: account/models.py:21
msgid "user"
msgstr "gebruiker"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-mailadres"

#: account/models.py:28
msgid "verified"
msgstr "geverifieerd"

#: account/models.py:29
msgid "primary"
msgstr "Primair"

#: account/models.py:35
msgid "email addresses"
msgstr "e-mailadressen"

#: account/models.py:141
msgid "created"
msgstr "aangemaakt"

#: account/models.py:142
msgid "sent"
msgstr "verstuurd"

#: account/models.py:143 socialaccount/models.py:65
msgid "key"
msgstr "sleutel"

#: account/models.py:148
msgid "email confirmation"
msgstr "e-mailadres bevestiging"

#: account/models.py:149
msgid "email confirmations"
msgstr "e-mailadres bevestigingen"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Je moet eerst je e-mailadres verifiëren voordat je twee-factor-authenticatie "
"kunt activeren."

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Je kunt geen e-mailadres toevoegen aan een account dat beveiligd is met twee-"
"factor-authenticatie is."

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr "Ongeldige code."

#: mfa/apps.py:7
msgid "MFA"
msgstr "MFA"

#: mfa/forms.py:15 mfa/forms.py:17
msgid "Code"
msgstr "Code"

#: mfa/forms.py:48
msgid "Authenticator code"
msgstr "Authenticator code"

#: mfa/models.py:15
msgid "Recovery codes"
msgstr "Herstelcodes"

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr "TOTP Authenticator"

#: socialaccount/adapter.py:32
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Er bestaat al een account met dit e-mailadres. Meld je eerst aan met dit "
"account, verbind daarna je %s account."

#: socialaccount/adapter.py:138
msgid "Your account has no password set up."
msgstr "Je account heeft geen wachtwoord ingesteld."

#: socialaccount/adapter.py:145
msgid "Your account has no verified email address."
msgstr "Je account heeft geen geverifieerd e-mailadres."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Sociale accounts"

#: socialaccount/models.py:39 socialaccount/models.py:93
msgid "provider"
msgstr "provider"

#: socialaccount/models.py:48
msgid "provider ID"
msgstr "provider ID"

#: socialaccount/models.py:52
msgid "name"
msgstr "naam"

#: socialaccount/models.py:54
msgid "client id"
msgstr "client id"

#: socialaccount/models.py:56
msgid "App ID, or consumer key"
msgstr "App ID, of consumer key"

#: socialaccount/models.py:59
msgid "secret key"
msgstr "geheime sleutel"

#: socialaccount/models.py:62
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, of consumer secret"

#: socialaccount/models.py:65
msgid "Key"
msgstr "Sleutel"

#: socialaccount/models.py:77
msgid "social application"
msgstr "social application"

#: socialaccount/models.py:78
msgid "social applications"
msgstr "social applications"

#: socialaccount/models.py:113
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:115
msgid "last login"
msgstr "laatst ingelogd"

#: socialaccount/models.py:116
msgid "date joined"
msgstr "datum toegetreden"

#: socialaccount/models.py:117
msgid "extra data"
msgstr "extra data"

#: socialaccount/models.py:121
msgid "social account"
msgstr "social account"

#: socialaccount/models.py:122
msgid "social accounts"
msgstr "social accounts"

#: socialaccount/models.py:156
msgid "token"
msgstr "token"

#: socialaccount/models.py:157
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:161
msgid "token secret"
msgstr ""

#: socialaccount/models.py:162
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "expires at"
msgstr "verloopt op"

#: socialaccount/models.py:170
msgid "social application token"
msgstr ""

#: socialaccount/models.py:171
msgid "social application tokens"
msgstr ""

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Ongeldige profiel data"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Ongeldig antwoord ontvangen tijdens het ophalen van een verzoeksleutel van "
"\"%s\", antwoord: \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr ""
"Ongeldig antwoord ontvangen tijdens het ophalen van een toegangssleutel van "
"\"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Geen verzoeksleutel opgeslagen voor \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Geen toegangssleutel opgeslagen voor \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Geen toegang tot privé data bij \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr ""
"Ongeldig antwoord ontvangen tijdens het ophalen van een verzoeksleutel van "
"\"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Account inactief"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Dit account is niet actief"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mailadressen"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "De volgende e-mailadressen zijn gekoppeld aan jouw account:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Geverifieerd"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Ongeverifieerd"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Primair"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Maak primair"

#: templates/account/email.html:45 templates/account/email_change.html:29
msgid "Re-send Verification"
msgstr "Stuur verificatie e-mail opnieuw"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Verwijder"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Voeg e-mailadres toe"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "E-mail toevoegen"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Wil je het geselecteerde e-mailadres echt verwijderen?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Je ontvangt deze e-mail omdat jij of iemand anders geprobeerd heeft een "
"account\n"
"te registreren met dit e-mail adres:\n"
"\n"
"%(email)s\n"
"\n"
"Echter, er bestaat al een account met dit e-mail adres.  Als je dit "
"vergeten\n"
"was, gebruik dan de wachtwoord vergeten procedure om de toegang tot je "
"account\n"
"te herstellen:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Account bestaat al"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Hallo van %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Bedankt voor het gebruiken van %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Je ontvangt deze e-mail omdat gebruiker %(user_display)s van\n"
"%(site_domain)s dit als e-mailadres heeft opgegeven om te koppelen\n"
"aan zijn of haar account.\n"
"\n"
"Om te bevestigen dat dit correct is, bezoek: %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Bevestig je e-mailadres"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Je ontvangt deze mail omdat er een verzoek is ingelegd om je wachtwoord "
"opnieuw\n"
"in te stellen. Je kunt deze gerust mail negeren als je dit niet zelf gedaan\n"
"hebt. Anders, klik op de volgende link om je wachtwoord opnieuw in te "
"stellen."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Deze link hoort bij je account met gebruikersnaam %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Nieuw wachtwoord"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Je ontvang deze e-mail omdat er een verzoek is ingelegd om het wachtwoord "
"te\n"
"veranderen van account met e-mailadres %(email)s. Echter, dit account "
"bestaat\n"
"niet.\n"
"\n"
"Je kunt deze e-mail negeren als jij dit verzoek niet zelf hebt ingelegd.\n"
"\n"
"Als jij dit wel hebt gedaan, dan kun je je via de volgende link registreren."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-mailadres"

#: templates/account/email_change.html:14
msgid "The following email address is associated with your account:"
msgstr "De volgende e-mailadressen zijn gekoppeld aan jouw account:"

#: templates/account/email_change.html:19
msgid "Your email address is still pending verification:"
msgstr "Je primaire e-mailadres wacht op verificatie."

#: templates/account/email_change.html:38
msgid "Change Email Address"
msgstr "Wijzig e-mailadres"

#: templates/account/email_change.html:49
#: templates/allauth/layouts/base.html:29
msgid "Change Email"
msgstr "E-mail wijzigen"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Bevestig e-mailadres"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Bevestig dat <a href=\"mailto:%(email)s\">%(email)s</a> een e-mailadres is "
"voor gebruiker %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:28
msgid "Confirm"
msgstr "Bevestigen"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Het e-mail adres %(email)s kon niet worden geverifieerd omdat het gekoppeld "
"is aan een ander account."

#: templates/account/email_confirm.html:35
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Deze e-mail verificatie link is verlopen of niet geldig. Dien een\n"
"<a href=\"%(email_url)s\">nieuw e-mail verificatie verzoek</a> in."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Aanmelden"

#: templates/account/login.html:12
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Als je nog geen account hebt <a href=\"%(signup_url)s\">registreer</a> je\n"
"dan eerst."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
msgid "Sign Out"
msgstr "Afmelden"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Weet je zeker dat je wilt afmelden?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "You kunt je primaire e-mailadres (%(email)s) niet verwijderen."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Bevestigings e-mail verzonden aan %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Je hebt het e-mailadres %(email)s bevestigd"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mailadres %(email)s verwijderd."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Je bent nu ingelogd als %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Je bent afgemeld."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Wachtwoord wijziging geslaagd."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Je wachtwoord is gewijzigd."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primair e-mailadres ingesteld."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Je primaire e-mailadres moet geverifieerd zijn."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Wachtwoord wijzigen"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Wachtwoord vergeten?"

#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Nieuw wachtwoord"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Wachtwoord vergeten? Vul je e-mailadres in en we sturen je een e-mail "
"waarmee je een nieuw wachtwoord kunt instellen."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Herstel mijn wachtwoord"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Neem a.u.b. contact met ons op als het niet lukt je wachtwoord opnieuw in te "
"stellen."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Volg de link in de verificatie e-mail om de controle af te ronden. Neem a.u."
"b. contact met ons op als je de e-mail niet binnen enkele minuten ontvangt."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Ongeldige sleutel"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"De link om je wachtwoord opnieuw in te stellen is niet geldig. Mogelijk is "
"deze al een keer gebruikt. <a href=\"%(passwd_reset_url)s\">Herstel je "
"wachtwoord</a> opnieuw."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Je wachtwoord is gewijzigd."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Zet wachtwoord"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
msgid "Confirm Access"
msgstr "Toegang bevestigen"

#: templates/account/reauthenticate.html:12
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""
"Om de beveiliging van je account te waarborgen, voert eerst je wachtwoord in:"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registreren"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Registreren"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Heb je al een account? <a href=\"%(login_url)s\">Meld je dan aan</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registratie gesloten"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Helaas, maar je kunt je momenteel niet registreren."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Notitie"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Je bent al ingelogd als %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Waarschuwing:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Het is raadzaam een e-mailadres toe te voegen zodat je notificaties kunt "
"ontvangen, je wachtwoord opnieuw kunt instellen, enz."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifieer je e-mailadres"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"We hebben een e-mail verstuurd ter verificatie. Volg de link in deze mail om "
"je registratie af te ronden. Neem a.u.b. contact met ons op als je deze e-"
"mail niet binnen enkele minuten ontvangt."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Voor dit gedeelte van de site is het nodig dat we je identiteit\n"
"verifiëren. Via een verificatie e-mail kunnen we controleren dat je\n"
"daadwerkelijk toegang hebt tot het opgegeven e-mailadres."

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Volg de link in de verificatie e-mail om de controle af te ronden. Neem a.u."
"b. contact met ons op als je de e-mail niet binnen enkele minuten ontvangt."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Merk op:</strong> je kunt altijd <a href=\"%(email_url)s\">je e-mail "
"adres wijzigen</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Berichten:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Twee-factor-authenticatie"

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Je account is beveiligd met twee-factor-authenticatie. Voer alstublieft een "
"authenticatiecode in:"

#: templates/mfa/index.html:13 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Authenticator-app"

#: templates/mfa/index.html:17
msgid "Authentication using an authenticator app is active."
msgstr "Authenticatie middels een authenticator-app is actief."

#: templates/mfa/index.html:19
msgid "An authenticator app is not active."
msgstr "Een authenticator-app is not actief."

#: templates/mfa/index.html:27 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deactiveer"

#: templates/mfa/index.html:31 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Activeer"

#: templates/mfa/index.html:39 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Herstelcodes"

#: templates/mfa/index.html:44 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Er is nog %(unused_count)s van de %(total_count)s herstelcodes beschikbaar."
msgstr[1] ""
"Er zijn nog %(unused_count)s van de %(total_count)s herstelcodes beschikbaar."

#: templates/mfa/index.html:47
msgid "No recovery codes set up."
msgstr "Er zijn geen herstelcodes ingesteld."

#: templates/mfa/index.html:56
msgid "View"
msgstr "Bekijk"

#: templates/mfa/index.html:62
msgid "Download"
msgstr "Downloaden"

#: templates/mfa/index.html:70 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Genereer"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Er zijn nieuwe herstelcodes gegenereerd."

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Authenticator-app geactiveerd."

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Authenticator-app gedeactiveerd."

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Je staat op het punt nieuwe herstelcodes te genereren voor je account."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Hierdoor zullen eerdere herstelcodes niet langer werken."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Weet je dit zeker?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Ongebruikte codes"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Codes downloaden"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Nieuwe codes genereren"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Activeer authenticator-app"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Beveilig je account met twee-factor-authenticatie. Scan eerst de "
"onderstaande QR code met je authenticator app, en vul vervolgens de "
"resulterende verificatie code in."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Authenticator geheim"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Je kunt deze code bewaren en later gebruiken om je authenticator app opnieuw "
"te installeren."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deactiveer authenticator-app"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Je staat op het punt authenticatie middels een authenticator-app te "
"deactiveren. Wil je dit echt?"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Social Network Login Failure"
msgstr "Aanmelden Mislukt"

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Er is een fout opgetreden toen we je wilde inloggen via je externe account."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Account Connecties"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Je kunt jezelf aanmelden met een van de volgende externe accounts:"

#: templates/socialaccount/connections.html:45
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Je hebt momenteel geen externe accounts gekoppeld."

#: templates/socialaccount/connections.html:48
msgid "Add a Third-Party Account"
msgstr "Voeg een extern account toe"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Koppel %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Je staat op het punt om een nieuw account van %(provider)s aan je account te "
"koppelen."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Aanmelden via %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Je staat op het punt om jezelf aan te melden via %(provider)s."

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "Ga verder"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Aanmelden geannuleerd"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Je hebt het aanmelden via een extern account geannuleerd. Als dit een "
"vergissing was, <a href=\"%(login_url)s\">meld je dan opnieuw aan</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Het externe account is gekoppeld."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Dit externe account is al gekoppeld aan een ander account."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Het externe account is ontkoppeld."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Om bij %(site_name)s in te kunnen loggen via %(provider_name)s hebben we de "
"volgende gegevens nodig:"

#: templates/socialaccount/snippets/login.html:8
msgid "Or use a third-party"
msgstr "Of gebruik een derde partij"

#, fuzzy
#~| msgid "Generate"
#~ msgid "Regenerate"
#~ msgstr "Genereer"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a href=\"%(signup_url)s"
#~ "\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Meld je aan met een van je bestaande externe accounts. Of, <a \n"
#~ "href=\"%(signup_url)s\">registreer</a> voor een %(site_name)s account en "
#~ "meld je hiermee aan:"

#~ msgid "or"
#~ msgstr "of"

#~ msgid "change password"
#~ msgstr "Wachtwoord wijzigen"

#~ msgid "OpenID Sign In"
#~ msgstr "Aanmelden via OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Dit e-mailadres is al geassocieerd met een ander account."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "We hebben je een e-mail verstuurd. Neem a.u.b. contact met ons op als je "
#~ "deze niet binnen enkele minuten ontvangen hebt."

#~ msgid "Account"
#~ msgstr "Account"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Je login en wachtwoord komen niet overeen."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Gebruikersnamen mogen alleen letters, cijfers en @/./+/-/_ bevatten."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Deze gebruikersnaam is al in gebruik. Kies a.u.b. een andere naam."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Aanmelden"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Je hebt bevestigd dat <a href=\"mailto:%(email)s\">%(email)s</a> een e-"
#~ "mail adres is voor gebruiker %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Bedankt voor het gebruik van onze site!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Bevestigings e-mail verzonden aan %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Verwijder wachtwoord"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr ""
#~ "Je kunt je wachtwoord verwijderen omdat je via OpenID bent ingelogd."

#~ msgid "delete my password"
#~ msgstr "Verwijder mijn wachtwoord"

#~ msgid "Password Deleted"
#~ msgstr "Wachtwoord verwijderd"

#~ msgid "Your password has been deleted."
#~ msgstr "Je wachtwoord is verwijderd."
