# Generated by Django 5.1.7 on 2025-05-09 22:15

import properties.models
import properties.storage
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0005_image_image_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='PENDING', max_length=20),
        ),
        migrations.AlterField(
            model_name='image',
            name='image',
            field=models.ImageField(storage=properties.storage.Base64Storage(), upload_to=properties.models.property_image_path),
        ),
    ]
