INFO 2025-03-14 00:43:52,731 autoreload 12580 14328 Watching for file changes with StatReloader
INFO 2025-03-16 17:34:39,293 autoreload 27004 14880 Watching for file changes with StatReloader
INFO 2025-03-16 17:39:00,032 autoreload 26516 11904 Watching for file changes with StatReloader
WARNING 2025-03-16 17:44:00,452 log 26516 21444 Not Found: /
WARNING 2025-03-16 17:44:00,454 basehttp 26516 21444 "GET / HTTP/1.1" 404 2812
WARNING 2025-03-16 17:44:00,660 log 26516 21444 Not Found: /favicon.ico
WARNING 2025-03-16 17:44:00,661 basehttp 26516 21444 "GET /favicon.ico HTTP/1.1" 404 2863
INFO 2025-03-16 17:44:27,338 basehttp 26516 21444 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-03-16 17:44:27,381 basehttp 26516 19036 "GET /api/docs/ HTTP/1.1" 200 4692
WARNING 2025-03-16 17:44:27,447 basehttp 26516 19036 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 404 1976
WARNING 2025-03-16 17:44:27,476 basehttp 26516 17232 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" 404 1994
WARNING 2025-03-16 17:44:27,479 basehttp 26516 5152 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 404 2027
WARNING 2025-03-16 17:44:27,510 basehttp 26516 26892 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 404 1985
INFO 2025-03-16 17:45:07,518 basehttp 26516 28532 "GET /api/docs/ HTTP/1.1" 200 4692
WARNING 2025-03-16 17:45:07,604 basehttp 26516 28532 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 404 1976
WARNING 2025-03-16 17:45:07,628 basehttp 26516 27316 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" 404 1994
WARNING 2025-03-16 17:45:07,629 basehttp 26516 28052 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 404 2027
INFO 2025-03-16 17:45:42,318 basehttp 26516 2500 "GET /api/schema/ HTTP/1.1" 200 13005
INFO 2025-03-16 17:48:16,086 autoreload 27004 14880 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-16 17:48:16,351 autoreload 26516 11904 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-16 17:48:17,070 autoreload 21416 20272 Watching for file changes with StatReloader
INFO 2025-03-16 17:48:17,470 autoreload 23236 24884 Watching for file changes with StatReloader
INFO 2025-03-16 17:48:31,547 autoreload 21416 20272 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-16 17:48:31,874 autoreload 23236 24884 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-16 17:48:32,640 autoreload 26264 30364 Watching for file changes with StatReloader
INFO 2025-03-16 17:48:32,869 autoreload 28832 30288 Watching for file changes with StatReloader
INFO 2025-03-16 17:49:05,283 basehttp 26264 24064 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-16 17:49:05,509 basehttp 26264 15892 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 229223
INFO 2025-03-16 17:49:05,512 basehttp 26264 24064 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 154985
INFO 2025-03-16 17:49:05,522 basehttp 26264 27916 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" ***********
INFO 2025-03-16 17:49:05,824 basehttp 26264 27916 "GET /api/schema/ HTTP/1.1" 200 28662
INFO 2025-03-16 17:50:49,532 autoreload 24024 29640 Watching for file changes with StatReloader
INFO 2025-03-17 21:59:52,740 basehttp 26264 19556 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 200 628
INFO 2025-03-19 19:45:40,387 autoreload 2484 9480 Watching for file changes with StatReloader
WARNING 2025-03-19 19:45:45,439 log 2484 5468 Not Found: /
WARNING 2025-03-19 19:45:45,442 basehttp 2484 5468 "GET / HTTP/1.1" 404 2812
WARNING 2025-03-19 19:45:45,755 log 2484 5468 Not Found: /favicon.ico
WARNING 2025-03-19 19:45:45,756 basehttp 2484 5468 "GET /favicon.ico HTTP/1.1" 404 2863
INFO 2025-03-19 19:45:56,502 basehttp 2484 5468 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-19 19:45:57,004 basehttp 2484 5468 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 154985
INFO 2025-03-19 19:45:57,033 basehttp 2484 13068 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 229223
INFO 2025-03-19 19:45:57,103 basehttp 2484 18092 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" ***********
INFO 2025-03-19 19:45:57,446 basehttp 2484 18092 "GET /api/schema/ HTTP/1.1" 200 28662
INFO 2025-03-22 15:05:35,062 autoreload 29592 25304 Watching for file changes with StatReloader
INFO 2025-03-22 15:10:33,544 autoreload 27196 4160 Watching for file changes with StatReloader
INFO 2025-03-22 15:11:13,339 autoreload 27572 7520 Watching for file changes with StatReloader
INFO 2025-03-22 15:11:49,228 autoreload 27572 7520 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-03-22 15:11:49,960 autoreload 19780 22560 Watching for file changes with StatReloader
INFO 2025-03-22 15:12:00,440 basehttp 19780 5020 "GET / HTTP/1.1" 302 0
INFO 2025-03-22 15:12:00,478 basehttp 19780 5020 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-22 15:12:00,590 basehttp 19780 5020 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 304 0
INFO 2025-03-22 15:12:00,590 basehttp 19780 22280 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" 304 0
INFO 2025-03-22 15:12:00,591 basehttp 19780 30656 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 304 0
ERROR 2025-03-22 15:12:01,099 log 19780 30656 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 285, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 256, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 112, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1397, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1453, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1048, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 684, in _map_serializer_field
    component = self.resolve_serializer(field, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1048, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 684, in _map_serializer_field
    component = self.resolve_serializer(field, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1042, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `id` is not valid for model `User`.
ERROR 2025-03-22 15:12:01,107 basehttp 19780 30656 "GET /api/schema/ HTTP/1.1" 500 243149
ERROR 2025-03-22 15:12:01,241 log 19780 30656 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 285, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 256, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 112, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1397, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1453, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1048, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 684, in _map_serializer_field
    component = self.resolve_serializer(field, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1048, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 684, in _map_serializer_field
    component = self.resolve_serializer(field, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1042, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `id` is not valid for model `User`.
ERROR 2025-03-22 15:12:01,245 basehttp 19780 30656 "GET /api/schema/ HTTP/1.1" 500 243149
INFO 2025-03-22 15:12:43,174 autoreload 19780 22560 C:\Local\Projects\avensus\Broadleaf\backend\users\serializers.py changed, reloading.
INFO 2025-03-22 15:12:43,868 autoreload 27704 28752 Watching for file changes with StatReloader
INFO 2025-03-22 15:12:56,184 basehttp 27704 21384 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-22 15:12:56,336 basehttp 27704 21384 "GET /api/schema/ HTTP/1.1" 200 77073
INFO 2025-03-22 15:13:03,900 basehttp 27704 21384 "GET / HTTP/1.1" 302 0
INFO 2025-03-22 15:13:03,918 basehttp 27704 21384 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-22 15:13:04,125 basehttp 27704 21384 "GET /api/schema/ HTTP/1.1" 200 77073
INFO 2025-03-22 15:23:30,965 autoreload 29520 12336 Watching for file changes with StatReloader
INFO 2025-03-22 15:23:38,761 basehttp 29520 2712 "GET / HTTP/1.1" 302 0
INFO 2025-03-22 15:23:38,792 basehttp 29520 2712 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-22 15:23:39,131 basehttp 29520 2712 "GET /api/schema/ HTTP/1.1" 200 77073
INFO 2025-03-22 15:23:47,455 basehttp 29520 2712 "GET /api/schema/ HTTP/1.1" 200 36174
INFO 2025-03-22 16:36:39,756 autoreload 29352 10520 Watching for file changes with StatReloader
INFO 2025-03-22 16:37:38,207 autoreload 14076 14640 Watching for file changes with StatReloader
INFO 2025-03-22 16:37:51,938 basehttp 14076 7348 "GET / HTTP/1.1" 302 0
INFO 2025-03-22 16:37:52,004 basehttp 14076 7348 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-22 16:37:52,450 basehttp 14076 7348 "GET /api/schema/ HTTP/1.1" 200 77073
INFO 2025-03-22 16:42:17,662 autoreload 23516 16144 Watching for file changes with StatReloader
WARNING 2025-03-22 16:45:56,378 log 23516 27116 Not Found: /api/token
WARNING 2025-03-22 16:45:56,380 basehttp 23516 27116 "POST /api/token HTTP/1.1" 404 3347
WARNING 2025-03-22 16:47:08,190 log 23516 29136 Not Found: /api/token/
WARNING 2025-03-22 16:47:08,192 basehttp 23516 29136 "POST /api/token/ HTTP/1.1" 404 3350
INFO 2025-03-22 16:47:25,234 autoreload 23516 16144 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-03-22 16:47:26,031 autoreload 24584 28604 Watching for file changes with StatReloader
INFO 2025-03-22 16:50:18,696 autoreload 24584 28604 C:\Local\Projects\avensus\Broadleaf\backend\users\models.py changed, reloading.
INFO 2025-03-22 16:50:19,370 autoreload 22220 26772 Watching for file changes with StatReloader
ERROR 2025-03-22 16:51:03,060 log 22220 25412 Internal Server Error: /api/token/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework_simplejwt\views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 429, in run_validation
    value = self.validate(value)
            ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework_simplejwt\serializers.py", line 75, in validate
    refresh = self.get_token(self.user)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework_simplejwt\serializers.py", line 66, in get_token
    return cls.token_class.for_user(user)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework_simplejwt\tokens.py", line 238, in for_user
    user_id = getattr(user, api_settings.USER_ID_FIELD)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'id'
ERROR 2025-03-22 16:51:03,067 basehttp 22220 25412 "POST /api/token/ HTTP/1.1" 500 121664
INFO 2025-03-22 16:52:23,101 autoreload 22220 26772 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-22 16:52:23,831 autoreload 28112 27836 Watching for file changes with StatReloader
WARNING 2025-03-22 16:53:02,307 log 28112 18400 Bad Request: /api/token/
WARNING 2025-03-22 16:53:02,309 basehttp 28112 18400 "POST /api/token/ HTTP/1.1" 400 76
WARNING 2025-03-22 16:53:07,937 log 28112 9900 Bad Request: /api/token/
WARNING 2025-03-22 16:53:07,937 basehttp 28112 9900 "POST /api/token/ HTTP/1.1" 400 76
WARNING 2025-03-22 16:54:20,388 log 28112 18592 Bad Request: /api/token/
WARNING 2025-03-22 16:54:20,389 basehttp 28112 18592 "POST /api/token/ HTTP/1.1" 400 73
WARNING 2025-03-22 16:54:46,206 log 28112 16924 Bad Request: /api/token/
WARNING 2025-03-22 16:54:46,206 basehttp 28112 16924 "POST /api/token/ HTTP/1.1" 400 73
ERROR 2025-03-22 16:55:43,444 log 28112 18004 Internal Server Error: /api/token
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\utils\deprecation.py", line 131, in __call__
    response = self.process_response(request, response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\middleware\common.py", line 108, in process_response
    return self.response_redirect_class(self.get_full_path_with_slash(request))
                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\middleware\common.py", line 87, in get_full_path_with_slash
    raise RuntimeError(
RuntimeError: You called this URL via POST, but the URL doesn't end in a slash and you have APPEND_SLASH set. Django can't redirect to the slash URL while maintaining POST data. Change your form to point to localhost:8000/api/token/ (note the trailing slash), or set APPEND_SLASH=False in your Django settings.
ERROR 2025-03-22 16:55:43,446 basehttp 28112 18004 "POST /api/token HTTP/1.1" 500 77463
WARNING 2025-03-22 16:55:51,243 log 28112 28160 Bad Request: /api/token/
WARNING 2025-03-22 16:55:51,245 basehttp 28112 28160 "POST /api/token/ HTTP/1.1" 400 73
INFO 2025-03-22 16:56:38,649 basehttp 28112 28732 "POST /api/token/ HTTP/1.1" 200 483
INFO 2025-03-22 16:58:05,550 basehttp 28112 17340 "GET /api/properties/ HTTP/1.1" 200 183
WARNING 2025-03-22 16:58:18,992 log 28112 2664 Unauthorized: /api/properties/properties/
WARNING 2025-03-22 16:58:18,993 basehttp 28112 2664 "GET /api/properties/properties/ HTTP/1.1" 401 58
WARNING 2025-03-22 16:58:19,141 log 28112 2664 Not Found: /favicon.ico
WARNING 2025-03-22 16:58:19,143 basehttp 28112 2664 "GET /favicon.ico HTTP/1.1" 404 3714
WARNING 2025-03-22 16:58:31,195 log 28112 2664 Unauthorized: /api/properties/images/
WARNING 2025-03-22 16:58:31,201 basehttp 28112 2664 "GET /api/properties/images/ HTTP/1.1" 401 58
WARNING 2025-03-22 16:58:34,488 log 28112 2664 Unauthorized: /api/properties/inquiries/
WARNING 2025-03-22 16:58:34,490 basehttp 28112 2664 "GET /api/properties/inquiries/ HTTP/1.1" 401 58
INFO 2025-03-22 16:59:02,304 basehttp 28112 17340 "GET /api/properties/properties/ HTTP/1.1" 200 2
INFO 2025-03-22 17:12:53,876 autoreload 28112 27836 C:\Local\Projects\avensus\Broadleaf\backend\users\models.py changed, reloading.
INFO 2025-03-22 17:12:54,864 autoreload 952 27652 Watching for file changes with StatReloader
INFO 2025-03-22 17:13:17,259 autoreload 952 27652 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-22 17:13:18,045 autoreload 23720 26168 Watching for file changes with StatReloader
INFO 2025-03-22 17:13:57,386 autoreload 23720 26168 C:\Local\Projects\avensus\Broadleaf\backend\users\models.py changed, reloading.
INFO 2025-03-22 17:13:58,138 autoreload 2096 6708 Watching for file changes with StatReloader
INFO 2025-03-22 17:14:07,036 autoreload 2096 6708 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-03-22 17:14:07,736 autoreload 29352 26180 Watching for file changes with StatReloader
INFO 2025-03-22 17:14:18,756 autoreload 29352 26180 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-03-22 17:14:19,404 autoreload 27384 22612 Watching for file changes with StatReloader
INFO 2025-03-22 17:15:09,664 autoreload 23344 23900 Watching for file changes with StatReloader
INFO 2025-03-22 17:17:59,019 autoreload 27384 22612 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-03-22 17:17:59,748 autoreload 28252 3140 Watching for file changes with StatReloader
INFO 2025-03-22 17:19:45,693 autoreload 28252 3140 C:\Local\Projects\avensus\Broadleaf\backend\properties\admin.py changed, reloading.
INFO 2025-03-22 17:19:46,390 autoreload 7004 30024 Watching for file changes with StatReloader
INFO 2025-03-22 17:19:58,463 autoreload 7004 30024 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-03-22 17:19:59,241 autoreload 28248 23724 Watching for file changes with StatReloader
INFO 2025-03-22 17:20:14,332 autoreload 28248 23724 C:\Local\Projects\avensus\Broadleaf\backend\properties\views.py changed, reloading.
INFO 2025-03-22 17:20:14,977 autoreload 25020 6068 Watching for file changes with StatReloader
INFO 2025-03-22 17:20:23,866 autoreload 25020 6068 C:\Local\Projects\avensus\Broadleaf\backend\properties\urls.py changed, reloading.
INFO 2025-03-22 17:20:24,510 autoreload 29552 30112 Watching for file changes with StatReloader
INFO 2025-03-22 17:27:00,160 autoreload 29552 30112 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-03-22 17:27:00,925 autoreload 25492 24684 Watching for file changes with StatReloader
INFO 2025-03-22 17:27:06,747 autoreload 25492 24684 C:\Local\Projects\avensus\Broadleaf\backend\properties\admin.py changed, reloading.
INFO 2025-03-22 17:27:07,459 autoreload 22136 25880 Watching for file changes with StatReloader
INFO 2025-03-22 17:27:38,466 autoreload 22136 25880 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-03-22 17:27:39,170 autoreload 20704 29584 Watching for file changes with StatReloader
INFO 2025-03-22 17:27:44,940 autoreload 20704 29584 C:\Local\Projects\avensus\Broadleaf\backend\properties\urls.py changed, reloading.
INFO 2025-03-22 17:27:45,626 autoreload 20692 2664 Watching for file changes with StatReloader
INFO 2025-03-22 17:28:40,546 basehttp 20692 26444 "GET /api/users/users/ HTTP/1.1" 200 246
WARNING 2025-03-22 17:30:43,432 log 20692 18892 Not Found: /api/transactions/transactions/
WARNING 2025-03-22 17:30:43,434 basehttp 20692 18892 "GET /api/transactions/transactions/ HTTP/1.1" 404 3771
INFO 2025-03-26 19:12:37,284 autoreload 38976 35676 Watching for file changes with StatReloader
INFO 2025-03-31 14:06:38,698 autoreload 21904 12980 Watching for file changes with StatReloader
INFO 2025-03-31 14:16:22,913 autoreload 17476 22552 Watching for file changes with StatReloader
INFO 2025-03-31 14:16:47,341 basehttp 17476 22704 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-03-31 14:16:47,416 basehttp 17476 24496 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-31 14:16:47,694 basehttp 17476 24496 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 154985
INFO 2025-03-31 14:16:47,701 basehttp 17476 24496 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 229223
INFO 2025-03-31 14:16:47,768 basehttp 17476 25668 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" ***********
INFO 2025-03-31 14:16:48,011 basehttp 17476 24496 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 200 628
INFO 2025-03-31 14:16:48,099 basehttp 17476 25668 "GET /api/schema/ HTTP/1.1" 200 103133
INFO 2025-03-31 12:59:40,057 autoreload 7 139697020455808 Watching for file changes with StatReloader
INFO 2025-03-31 13:26:44,618 autoreload 7 140092035357568 Watching for file changes with StatReloader
INFO 2025-03-31 13:28:47,866 autoreload 7 140464155806592 Watching for file changes with StatReloader
INFO 2025-03-31 13:30:04,615 autoreload 7 140321787894656 Watching for file changes with StatReloader
WARNING 2025-03-31 13:30:33,181 log 7 140321705948864 Not Found: /health
WARNING 2025-03-31 13:30:33,182 basehttp 7 140321705948864 "GET /health HTTP/1.1" 404 3699
INFO 2025-03-31 13:30:51,782 basehttp 7 140321696507584 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-03-31 13:30:51,887 basehttp 7 140321696507584 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 154985
INFO 2025-03-31 13:30:51,890 basehttp 7 140321480505024 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 229223
INFO 2025-03-31 13:30:51,916 basehttp 7 140321705948864 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" ***********
INFO 2025-03-31 13:30:52,113 basehttp 7 140321480505024 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 200 628
INFO 2025-03-31 13:30:52,251 basehttp 7 140321705948864 "GET /api/schema/ HTTP/1.1" 200 103177
WARNING 2025-03-31 13:31:03,282 log 7 140321472112320 Not Found: /health
WARNING 2025-03-31 13:31:03,283 basehttp 7 140321472112320 "GET /health HTTP/1.1" 404 3699
INFO 2025-04-01 13:58:59,894 autoreload 31220 11180 Watching for file changes with StatReloader
INFO 2025-04-01 13:59:25,980 basehttp 31220 35520 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-04-01 13:59:26,093 basehttp 31220 35520 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 304 0
INFO 2025-04-01 13:59:26,104 basehttp 31220 35364 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 304 0
INFO 2025-04-01 13:59:26,104 basehttp 31220 35520 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" 304 0
INFO 2025-04-01 13:59:26,369 basehttp 31220 35520 "GET /api/schema/ HTTP/1.1" 200 103133
WARNING 2025-04-01 22:24:02,234 log 15380 31512 Bad Request: /api/token/
WARNING 2025-04-01 22:24:03,335 log 15380 31512 Bad Request: /api/token/
WARNING 2025-04-01 22:24:04,446 log 15380 31512 Unauthorized: /api/users/users/
WARNING 2025-04-02 11:44:32,815 log 38372 34560 Bad Request: /api/token/
WARNING 2025-04-02 11:44:33,626 log 38372 34560 Bad Request: /api/token/
WARNING 2025-04-02 11:44:34,427 log 38372 34560 Unauthorized: /api/users/users/
WARNING 2025-04-02 11:46:02,887 log 37792 31144 Unauthorized: /api/token/
WARNING 2025-04-02 11:46:03,664 log 37792 31144 Unauthorized: /api/users/users/
WARNING 2025-04-02 11:47:12,428 log 26268 37932 Unauthorized: /api/token/
WARNING 2025-04-02 11:47:13,199 log 26268 37932 Unauthorized: /api/users/users/
WARNING 2025-04-02 11:47:14,737 log 26268 37932 Unauthorized: /api/users/users/
INFO 2025-04-13 23:08:43,823 autoreload 27004 33884 Watching for file changes with StatReloader
WARNING 2025-04-13 23:09:05,659 log 27004 33116 Unauthorized: /api/token/
WARNING 2025-04-13 23:09:05,659 basehttp 27004 33116 "POST /api/token/ HTTP/1.1" 401 63
INFO 2025-04-25 07:40:09,554 autoreload 38552 2464 Watching for file changes with StatReloader
INFO 2025-04-25 07:41:01,714 basehttp 38552 32880 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-04-25 07:41:02,197 basehttp 38552 32880 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 154985
INFO 2025-04-25 07:41:02,363 basehttp 38552 37324 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 229223
INFO 2025-04-25 07:41:02,419 basehttp 38552 17068 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" ***********
INFO 2025-04-25 07:41:02,619 basehttp 38552 37324 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 200 628
INFO 2025-04-25 07:41:02,803 basehttp 38552 17068 "GET /api/schema/ HTTP/1.1" 200 103133
ERROR 2025-04-25 07:41:17,162 log 38552 17068 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/users/list.html
ERROR 2025-04-25 07:41:17,177 basehttp 38552 17068 "GET /web/users/ HTTP/1.1" 500 91091
WARNING 2025-04-25 07:41:17,315 log 38552 17068 Not Found: /favicon.ico
WARNING 2025-04-25 07:41:17,315 basehttp 38552 17068 "GET /favicon.ico HTTP/1.1" 404 3861
INFO 2025-04-25 08:00:01,238 autoreload 38552 2464 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-25 08:00:02,422 autoreload 38900 36408 Watching for file changes with StatReloader
ERROR 2025-04-25 08:00:20,469 log 38900 35616 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_detail' with arguments '('',)' not found. 1 pattern(s) tried: ['web/users/(?P<pk>[0-9]+)/\\Z']
ERROR 2025-04-25 08:00:20,481 basehttp 38900 35616 "GET /web/users/ HTTP/1.1" 500 186792
INFO 2025-04-25 08:01:37,702 autoreload 38900 36408 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:01:38,916 autoreload 26416 38484 Watching for file changes with StatReloader
INFO 2025-04-25 08:01:54,928 autoreload 26416 38484 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:01:55,982 autoreload 12936 37200 Watching for file changes with StatReloader
ERROR 2025-04-25 08:02:01,625 log 12936 37592 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_detail' with arguments '('',)' not found. 1 pattern(s) tried: ['web/users/(?P<pk>[0-9]+)/\\Z']
ERROR 2025-04-25 08:02:01,641 basehttp 12936 37592 "GET /web/users/ HTTP/1.1" 500 173933
ERROR 2025-04-25 08:02:05,386 log 12936 37592 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_detail' with arguments '('',)' not found. 1 pattern(s) tried: ['web/users/(?P<pk>[0-9]+)/\\Z']
ERROR 2025-04-25 08:02:05,396 basehttp 12936 37592 "GET /web/users/ HTTP/1.1" 500 173933
WARNING 2025-04-25 08:02:24,071 log 12936 37592 Unauthorized: /api/users/
WARNING 2025-04-25 08:02:24,073 basehttp 12936 37592 "GET /api/users/ HTTP/1.1" 401 58
ERROR 2025-04-25 08:02:33,286 log 12936 37592 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_detail' with arguments '('',)' not found. 1 pattern(s) tried: ['web/users/(?P<pk>[0-9]+)/\\Z']
ERROR 2025-04-25 08:02:33,292 basehttp 12936 37592 "GET /web/users/ HTTP/1.1" 500 173933
INFO 2025-04-25 08:03:14,913 autoreload 12936 37200 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:03:16,258 autoreload 24960 7064 Watching for file changes with StatReloader
INFO 2025-04-25 08:03:38,245 autoreload 24960 7064 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:03:39,247 autoreload 20372 28984 Watching for file changes with StatReloader
INFO 2025-04-25 08:04:52,243 autoreload 20372 28984 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:04:53,150 autoreload 1856 19364 Watching for file changes with StatReloader
INFO 2025-04-25 08:04:57,165 autoreload 1856 19364 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:04:58,157 autoreload 22820 23652 Watching for file changes with StatReloader
ERROR 2025-04-25 08:05:04,183 log 22820 31420 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\list.py", line 154, in get
    self.object_list = self.get_queryset()
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 17, in get_queryset
    print("Users in queryset:", [user.id for user in queryset])  # Debug print
                                 ^^^^^^^
AttributeError: 'User' object has no attribute 'id'
ERROR 2025-04-25 08:05:04,183 basehttp 22820 31420 "GET /web/users/ HTTP/1.1" 500 90025
ERROR 2025-04-25 08:09:14,613 log 22820 31420 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\list.py", line 154, in get
    self.object_list = self.get_queryset()
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 17, in get_queryset
    print("Users in queryset:", [user.id for user in queryset])  # Debug print
                                 ^^^^^^^
AttributeError: 'User' object has no attribute 'id'
ERROR 2025-04-25 08:09:14,615 basehttp 22820 31420 "GET /web/users/ HTTP/1.1" 500 90025
INFO 2025-04-25 08:09:56,536 autoreload 22820 23652 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:09:57,512 autoreload 15604 37316 Watching for file changes with StatReloader
INFO 2025-04-25 08:10:02,554 autoreload 15604 37316 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:10:03,686 autoreload 11260 25852 Watching for file changes with StatReloader
INFO 2025-04-25 08:10:10,674 autoreload 11260 25852 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-25 08:10:11,622 autoreload 25552 13780 Watching for file changes with StatReloader
ERROR 2025-04-25 08:10:22,369 log 25552 18104 Internal Server Error: /web/users/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_detail' with arguments '('',)' not found. 1 pattern(s) tried: ['web/users/(?P<user_id>[0-9]+)/\\Z']
ERROR 2025-04-25 08:10:22,378 basehttp 25552 18104 "GET /web/users/ HTTP/1.1" 500 174043
INFO 2025-04-25 08:10:49,583 basehttp 25552 18104 "GET /web/users/ HTTP/1.1" 200 2339
WARNING 2025-04-25 08:10:49,635 basehttp 25552 33392 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-25 08:10:49,676 basehttp 25552 18104 "GET /static/web/css/style.css HTTP/1.1" 200 1
INFO 2025-04-25 08:10:58,725 basehttp 25552 18104 "GET /web/users/1/ HTTP/1.1" 200 3188
WARNING 2025-04-25 08:10:58,775 basehttp 25552 18104 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-25 08:11:05,083 basehttp 25552 4384 "GET /web/users/ HTTP/1.1" 200 2339
WARNING 2025-04-25 08:11:05,146 basehttp 25552 4384 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-25 08:11:07,895 basehttp 25552 22524 "GET /web/users/1/update/ HTTP/1.1" 200 3945
WARNING 2025-04-25 08:11:07,941 basehttp 25552 22524 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-25 08:12:14,936 autoreload 25552 13780 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:12:16,097 autoreload 19196 2124 Watching for file changes with StatReloader
INFO 2025-04-25 08:29:38,000 autoreload 19196 2124 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-25 08:29:38,965 autoreload 37660 17524 Watching for file changes with StatReloader
INFO 2025-04-25 08:29:42,589 basehttp 37660 25300 "GET /web/users/ HTTP/1.1" 302 0
INFO 2025-04-25 08:29:42,627 basehttp 37660 25300 "GET /web/signin/?next=/web/users/ HTTP/1.1" 200 2950
WARNING 2025-04-25 08:29:42,770 basehttp 37660 25300 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-25 08:29:46,341 autoreload 37660 17524 C:\Local\Projects\avensus\Broadleaf\backend\web\forms.py changed, reloading.
INFO 2025-04-25 08:29:47,314 autoreload 18064 36168 Watching for file changes with StatReloader
INFO 2025-04-25 08:29:53,478 autoreload 18064 36168 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-25 08:29:54,587 autoreload 22828 32696 Watching for file changes with StatReloader
INFO 2025-04-25 08:30:43,450 basehttp 22828 32360 "GET /web/signup/ HTTP/1.1" 200 3446
WARNING 2025-04-25 08:30:43,538 basehttp 22828 32360 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-25 08:30:47,303 basehttp 22828 32292 "GET /web/signin/ HTTP/1.1" 200 2950
WARNING 2025-04-25 08:30:47,399 basehttp 22828 32292 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-04-26 00:09:15,752 autoreload 22828 32696 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 00:09:17,746 autoreload 26748 19684 Watching for file changes with StatReloader
INFO 2025-04-26 00:10:06,135 autoreload 26748 19684 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-26 00:10:07,000 autoreload 38840 28724 Watching for file changes with StatReloader
INFO 2025-04-26 00:10:17,064 autoreload 38840 28724 C:\Local\Projects\avensus\Broadleaf\backend\web\forms.py changed, reloading.
INFO 2025-04-26 00:10:17,791 autoreload 10964 17416 Watching for file changes with StatReloader
INFO 2025-04-26 00:10:26,040 autoreload 10964 17416 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-26 00:10:27,220 autoreload 5296 22060 Watching for file changes with StatReloader
INFO 2025-04-26 00:11:00,431 autoreload 5296 22060 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-26 00:11:01,163 autoreload 32568 12652 Watching for file changes with StatReloader
INFO 2025-04-26 00:11:52,180 autoreload 32568 12652 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-26 00:11:52,888 autoreload 14060 19304 Watching for file changes with StatReloader
INFO 2025-04-26 00:12:08,971 autoreload 14060 19304 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-04-26 00:12:09,710 autoreload 36744 20800 Watching for file changes with StatReloader
INFO 2025-04-26 00:13:16,472 autoreload 36744 20800 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-26 00:13:17,150 autoreload 39564 32880 Watching for file changes with StatReloader
INFO 2025-04-26 00:14:21,461 autoreload 39564 32880 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-04-26 00:14:22,165 autoreload 18500 34108 Watching for file changes with StatReloader
INFO 2025-04-26 00:14:30,139 autoreload 18500 34108 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-26 00:14:30,867 autoreload 36840 38612 Watching for file changes with StatReloader
INFO 2025-04-26 00:14:40,919 autoreload 36840 38612 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-04-26 00:14:41,953 autoreload 15712 29836 Watching for file changes with StatReloader
INFO 2025-04-26 08:28:08,839 autoreload 4264 12560 Watching for file changes with StatReloader
INFO 2025-04-26 08:28:19,289 basehttp 4264 11424 "GET /web/users/ HTTP/1.1" 302 0
INFO 2025-04-26 08:28:19,376 basehttp 4264 11424 "GET /web/signin/?next=/web/users/ HTTP/1.1" 200 2950
INFO 2025-04-26 08:28:19,482 basehttp 4264 7120 "GET /static/web/css/style.css HTTP/1.1" 304 0
WARNING 2025-04-26 08:28:19,482 basehttp 4264 11424 "GET /static/web/js/main.js HTTP/1.1" 404 1856
WARNING 2025-04-26 08:28:19,720 log 4264 7120 Not Found: /favicon.ico
WARNING 2025-04-26 08:28:19,727 basehttp 4264 7120 "GET /favicon.ico HTTP/1.1" 404 3861
INFO 2025-04-26 08:29:06,703 basehttp 4264 7120 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-04-26 08:29:06,720 basehttp 4264 5596 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-04-26 08:29:07,036 basehttp 4264 5596 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 154985
INFO 2025-04-26 08:29:07,103 basehttp 4264 6992 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 229223
INFO 2025-04-26 08:29:07,107 basehttp 4264 5596 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" ***********
ERROR 2025-04-26 08:29:10,199 log 4264 5596 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 08:29:10,216 basehttp 4264 5596 "GET /api/schema/ HTTP/1.1" 500 345593
ERROR 2025-04-26 08:29:12,647 log 4264 5596 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 08:29:12,650 basehttp 4264 5596 "GET /api/schema/ HTTP/1.1" 500 345593
INFO 2025-04-26 08:29:59,282 autoreload 4264 12560 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 08:30:00,438 autoreload 10056 14980 Watching for file changes with StatReloader
INFO 2025-04-26 08:30:37,142 autoreload 10056 14980 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 08:30:38,087 autoreload 15000 18000 Watching for file changes with StatReloader
INFO 2025-04-26 08:34:33,610 basehttp 15000 5456 "GET /api/docs/ HTTP/1.1" 200 4708
ERROR 2025-04-26 08:34:38,291 log 15000 5456 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 08:34:38,302 basehttp 15000 5456 "GET /api/schema/ HTTP/1.1" 500 347993
ERROR 2025-04-26 08:34:41,063 log 15000 5456 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 08:34:41,069 basehttp 15000 5456 "GET /api/schema/ HTTP/1.1" 500 347993
INFO 2025-04-26 11:18:57,839 autoreload 15000 18000 C:\Local\Projects\avensus\Broadleaf\backend\users\authentication.py changed, reloading.
INFO 2025-04-26 11:18:58,866 autoreload 11060 4668 Watching for file changes with StatReloader
INFO 2025-04-26 11:19:08,174 autoreload 11060 4668 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 11:19:09,064 autoreload 12560 2956 Watching for file changes with StatReloader
INFO 2025-04-26 11:20:37,219 basehttp 12560 10520 "GET /api/docs/ HTTP/1.1" 200 4708
ERROR 2025-04-26 11:20:41,169 log 12560 10520 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:20:41,186 basehttp 12560 10520 "GET /api/schema/ HTTP/1.1" 500 348400
ERROR 2025-04-26 11:20:44,021 log 12560 10520 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:20:44,029 basehttp 12560 10520 "GET /api/schema/ HTTP/1.1" 500 348400
INFO 2025-04-26 11:21:10,379 autoreload 10084 11020 Watching for file changes with StatReloader
INFO 2025-04-26 11:21:15,661 basehttp 10084 6852 "GET /api/docs/ HTTP/1.1" 200 4708
ERROR 2025-04-26 11:21:17,420 log 10084 6852 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:21:17,427 basehttp 10084 6852 "GET /api/schema/ HTTP/1.1" 500 348400
ERROR 2025-04-26 11:21:20,750 log 10084 6852 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:21:20,750 basehttp 10084 6852 "GET /api/schema/ HTTP/1.1" 500 348400
INFO 2025-04-26 11:28:06,817 autoreload 10084 11020 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 11:28:08,000 autoreload 18644 10784 Watching for file changes with StatReloader
INFO 2025-04-26 11:28:13,218 autoreload 18644 10784 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 11:28:14,350 autoreload 16148 15780 Watching for file changes with StatReloader
INFO 2025-04-26 11:28:26,651 autoreload 16148 15780 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 11:28:27,737 autoreload 16736 7652 Watching for file changes with StatReloader
INFO 2025-04-26 11:28:54,632 basehttp 16736 7204 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-26 11:28:56,090 log 16736 7204 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:28:56,100 basehttp 16736 7204 "GET /api/schema/ HTTP/1.1" 500 343534
ERROR 2025-04-26 11:28:59,086 log 16736 7204 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:28:59,091 basehttp 16736 7204 "GET /api/schema/ HTTP/1.1" 500 343534
INFO 2025-04-26 11:30:18,689 autoreload 16736 7652 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 11:30:19,767 autoreload 17212 6544 Watching for file changes with StatReloader
INFO 2025-04-26 11:31:02,720 autoreload 17212 6544 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-26 11:31:03,653 autoreload 3264 13112 Watching for file changes with StatReloader
INFO 2025-04-26 11:31:12,691 basehttp 3264 6180 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-26 11:31:14,175 log 3264 6180 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:31:14,192 basehttp 3264 6180 "GET /api/schema/ HTTP/1.1" 500 341698
ERROR 2025-04-26 11:31:16,858 log 3264 6180 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-26 11:31:16,868 basehttp 3264 6180 "GET /api/schema/ HTTP/1.1" 500 341698
INFO 2025-04-26 11:31:35,995 autoreload 3308 3604 Watching for file changes with StatReloader
INFO 2025-04-26 11:32:03,274 basehttp 3308 16660 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-26 11:32:19,683 log 3308 14560 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\drf_spectacular\renderers.py", line 61, in render
    return yaml.dump(
           ^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\__init__.py", line 253, in dump
    return dump_all([data], stream, Dumper=Dumper, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\__init__.py", line 241, in dump_all
    dumper.represent(data)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 27, in represent
    node = self.represent_data(data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 207, in represent_dict
    return self.represent_mapping('tag:yaml.org,2002:map', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 118, in represent_mapping
    node_value = self.represent_data(item_value)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 48, in represent_data
    node = self.yaml_representers[data_types[0]](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 199, in represent_list
    return self.represent_sequence('tag:yaml.org,2002:seq', data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 92, in represent_sequence
    node_item = self.represent_data(item)
                ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 58, in represent_data
    node = self.yaml_representers[None](self, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\yaml\representer.py", line 231, in represent_undefined
    raise RepresenterError("cannot represent an object", data)
yaml.representer.RepresenterError: ('cannot represent an object', <drf_spectacular.utils.OpenApiExample object at 0x000001FF7CEB51F0>)
ERROR 2025-04-26 11:32:19,691 basehttp 3308 14560 "GET /api/schema/ HTTP/1.1" 500 357656
INFO 2025-04-26 11:33:20,189 autoreload 9772 17028 Watching for file changes with StatReloader
INFO 2025-04-27 23:53:04,808 autoreload 18828 19464 Watching for file changes with StatReloader
INFO 2025-04-27 23:53:27,615 basehttp 18828 26040 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-27 23:53:31,006 log 18828 26040 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-27 23:53:31,019 basehttp 18828 26040 "GET /api/schema/ HTTP/1.1" 500 341699
ERROR 2025-04-27 23:53:33,772 log 18828 26040 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-27 23:53:33,779 basehttp 18828 26040 "GET /api/schema/ HTTP/1.1" 500 341699
INFO 2025-04-27 23:55:42,159 autoreload 18828 19464 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-27 23:55:57,416 autoreload 19348 11120 Watching for file changes with StatReloader
INFO 2025-04-27 23:56:12,832 basehttp 19348 5464 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-27 23:56:16,007 log 19348 5464 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-27 23:56:16,021 basehttp 19348 5464 "GET /api/schema/ HTTP/1.1" 500 341856
ERROR 2025-04-27 23:56:18,784 log 19348 5464 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-27 23:56:18,792 basehttp 19348 5464 "GET /api/schema/ HTTP/1.1" 500 341856
INFO 2025-04-27 23:57:01,577 autoreload 19348 11120 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-27 23:57:38,171 autoreload 5976 28504 Watching for file changes with StatReloader
INFO 2025-04-27 23:57:45,550 basehttp 5976 8324 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-27 23:57:48,553 log 5976 8324 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-27 23:57:48,564 basehttp 5976 8324 "GET /api/schema/ HTTP/1.1" 500 341856
ERROR 2025-04-27 23:57:51,268 log 5976 8324 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-27 23:57:51,275 basehttp 5976 8324 "GET /api/schema/ HTTP/1.1" 500 341856
INFO 2025-04-27 23:59:17,583 autoreload 5976 28504 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-27 23:59:34,665 autoreload 23964 27220 Watching for file changes with StatReloader
INFO 2025-04-27 23:59:54,770 autoreload 23964 27220 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-04-28 00:00:10,718 autoreload 17764 22584 Watching for file changes with StatReloader
INFO 2025-04-28 00:00:18,829 basehttp 17764 24536 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-28 00:00:21,925 log 17764 24536 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-28 00:00:21,940 basehttp 17764 24536 "GET /api/schema/ HTTP/1.1" 500 343152
ERROR 2025-04-28 00:00:24,828 log 17764 24536 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-28 00:00:24,836 basehttp 17764 24536 "GET /api/schema/ HTTP/1.1" 500 343152
INFO 2025-04-28 00:05:35,315 autoreload 25760 22260 Watching for file changes with StatReloader
INFO 2025-04-28 00:07:50,562 basehttp 25760 10328 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-04-28 00:07:53,695 log 25760 10328 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-28 00:07:53,709 basehttp 25760 10328 "GET /api/schema/ HTTP/1.1" 500 343152
ERROR 2025-04-28 00:07:56,570 log 25760 10328 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-04-28 00:07:56,577 basehttp 25760 10328 "GET /api/schema/ HTTP/1.1" 500 343152
INFO 2025-04-28 00:11:18,261 autoreload 2264 16256 Watching for file changes with StatReloader
INFO 2025-04-28 00:11:26,700 basehttp 2264 3756 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-04-28 00:11:27,150 basehttp 2264 3756 "GET /api/schema/ HTTP/1.1" 200 103133
INFO 2025-04-28 00:11:27,196 basehttp 2264 28008 "GET /static/drf_spectacular_sidecar/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 200 628
INFO 2025-04-28 09:08:27,850 autoreload 27836 21928 Watching for file changes with StatReloader
INFO 2025-04-28 09:09:10,032 basehttp 27836 24900 "GET /api/docs/ HTTP/1.1" 200 4692
INFO 2025-04-28 09:09:10,353 basehttp 27836 24900 "GET /api/schema/ HTTP/1.1" 200 103133
INFO 2025-04-28 09:09:33,919 basehttp 27836 24900 "GET /web HTTP/1.1" 301 0
INFO 2025-04-28 09:09:33,929 basehttp 27836 21028 "GET /web/ HTTP/1.1" 200 569
WARNING 2025-04-28 09:09:34,032 basehttp 27836 21028 "GET /static/web/css/style.css HTTP/1.1" 404 1865
INFO 2025-04-29 14:48:33,942 autoreload 10744 2296 Watching for file changes with StatReloader
INFO 2025-04-29 14:49:06,269 basehttp 10744 4636 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-04-29 14:49:06,658 basehttp 10744 4636 "GET /api/schema/ HTTP/1.1" 200 108432
WARNING 2025-04-29 15:03:24,090 log 10744 18284 Unauthorized: /api/properties/properties/
WARNING 2025-04-29 15:03:24,092 basehttp 10744 18284 "GET /api/properties/properties/ HTTP/1.1" 401 172
WARNING 2025-04-29 15:04:11,815 log 10744 18284 Unauthorized: /api/token/
WARNING 2025-04-29 15:04:11,817 basehttp 10744 18284 "POST /api/token/ HTTP/1.1" 401 63
INFO 2025-04-29 15:04:33,797 basehttp 10744 18284 "POST /api/token/ HTTP/1.1" 200 483
INFO 2025-04-29 15:06:00,198 basehttp 10744 19404 "GET /api/properties/properties/ HTTP/1.1" 200 1784
INFO 2025-04-29 15:13:25,405 basehttp 10744 29976 "GET /api/properties/inquiries/ HTTP/1.1" 200 2
INFO 2025-04-29 15:14:03,876 basehttp 10744 29976 "GET /api/properties/transactions/ HTTP/1.1" 200 52
ERROR 2025-04-29 15:14:19,186 log 10744 29976 Internal Server Error: /api/properties/images/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 415, in initial
    self.check_permissions(request)
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\rest_framework\views.py", line 332, in check_permissions
    if not permission.has_permission(request, self):
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\users\permissions.py", line 8, in has_permission
    return request.user and request.user.is_authenticated and request.user.role.name == 'agent'
                                                              ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Role' object has no attribute 'name'
ERROR 2025-04-29 15:14:19,189 basehttp 10744 29976 "GET /api/properties/images/ HTTP/1.1" 500 112778
INFO 2025-04-29 15:15:55,131 autoreload 10744 2296 C:\Local\Projects\avensus\Broadleaf\backend\users\permissions.py changed, reloading.
INFO 2025-04-29 15:15:55,994 autoreload 31100 16916 Watching for file changes with StatReloader
WARNING 2025-04-29 15:16:26,081 log 31100 29932 Forbidden: /api/properties/images/
WARNING 2025-04-29 15:16:26,082 basehttp 31100 29932 "GET /api/properties/images/ HTTP/1.1" 403 63
INFO 2025-05-01 13:45:53,696 autoreload 26004 23352 Watching for file changes with StatReloader
INFO 2025-05-01 13:48:50,396 basehttp 26004 35032 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-05-01 13:48:51,045 log 26004 35032 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\generators.py", line 285, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\generators.py", line 256, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 112, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1397, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1453, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1042, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `created_at` is not valid for model `Image`.
ERROR 2025-05-01 13:48:51,063 basehttp 26004 35032 "GET /api/schema/ HTTP/1.1" 500 181729
ERROR 2025-05-01 13:48:51,243 log 26004 35032 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\generators.py", line 285, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\generators.py", line 256, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 112, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1397, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1453, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\drf_spectacular\openapi.py", line 1042, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `created_at` is not valid for model `Image`.
ERROR 2025-05-01 13:48:51,251 basehttp 26004 35032 "GET /api/schema/ HTTP/1.1" 500 181729
INFO 2025-05-01 14:04:04,639 autoreload 26004 23352 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-05-01 14:04:05,434 autoreload 11400 5876 Watching for file changes with StatReloader
INFO 2025-05-01 14:08:20,276 autoreload 11400 5876 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-05-01 14:08:21,195 autoreload 15016 13748 Watching for file changes with StatReloader
INFO 2025-05-01 14:09:23,995 autoreload 15016 13748 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-05-01 14:09:25,026 autoreload 37368 35936 Watching for file changes with StatReloader
INFO 2025-05-01 14:10:09,204 basehttp 37368 33064 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-05-01 14:10:12,064 log 37368 33064 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-05-01 14:10:12,085 basehttp 37368 33064 "GET /api/schema/ HTTP/1.1" 500 343426
ERROR 2025-05-01 14:10:14,440 log 37368 33064 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-05-01 14:10:14,447 basehttp 37368 33064 "GET /api/schema/ HTTP/1.1" 500 343426
INFO 2025-05-01 14:13:06,084 autoreload 37368 35936 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-01 14:13:07,252 autoreload 34664 37608 Watching for file changes with StatReloader
INFO 2025-05-01 14:13:16,006 basehttp 34664 39772 "GET /api/docs/ HTTP/1.1" 200 4646
ERROR 2025-05-01 14:13:18,196 log 34664 39772 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-05-01 14:13:18,207 basehttp 34664 39772 "GET /api/schema/ HTTP/1.1" 500 345441
ERROR 2025-05-01 14:13:21,074 log 34664 39772 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\renderers.py", line 99, in render
    ret = json.dumps(
          ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  [Previous line repeated 6 more times]
  File "C:\Python312\Lib\json\encoder.py", line 326, in _iterencode_list
    yield from chunks
  File "C:\Python312\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\utils\encoders.py", line 67, in default
    return super().default(obj)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type OpenApiExample is not JSON serializable
ERROR 2025-05-01 14:13:21,082 basehttp 34664 39772 "GET /api/schema/ HTTP/1.1" 500 345441
INFO 2025-05-01 14:16:27,089 autoreload 34664 37608 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-05-01 14:16:28,030 autoreload 38504 32336 Watching for file changes with StatReloader
INFO 2025-05-01 14:16:42,589 autoreload 38504 32336 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-01 14:16:43,544 autoreload 2244 38196 Watching for file changes with StatReloader
INFO 2025-05-01 14:17:00,166 basehttp 2244 34216 "GET /api/docs/ HTTP/1.1" 200 4708
ERROR 2025-05-01 14:17:00,912 log 2244 34216 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 310, in perform_content_negotiation
    return conneg.select_renderer(request, renderers, self.format_kwarg)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\negotiation.py", line 78, in select_renderer
    raise exceptions.NotAcceptable(available_renderers=renderers)
rest_framework.exceptions.NotAcceptable: Could not satisfy the request Accept header.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 431, in finalize_response
    neg = self.perform_content_negotiation(request, force=True)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 313, in perform_content_negotiation
    return (renderers[0], renderers[0].media_type)
            ~~~~~~~~~^^^
IndexError: list index out of range
ERROR 2025-05-01 14:17:00,916 basehttp 2244 34216 "GET /api/schema/ HTTP/1.1" 500 105335
ERROR 2025-05-01 14:17:00,948 log 2244 34216 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 310, in perform_content_negotiation
    return conneg.select_renderer(request, renderers, self.format_kwarg)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\negotiation.py", line 78, in select_renderer
    raise exceptions.NotAcceptable(available_renderers=renderers)
rest_framework.exceptions.NotAcceptable: Could not satisfy the request Accept header.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 431, in finalize_response
    neg = self.perform_content_negotiation(request, force=True)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 313, in perform_content_negotiation
    return (renderers[0], renderers[0].media_type)
            ~~~~~~~~~^^^
IndexError: list index out of range
ERROR 2025-05-01 14:17:00,951 basehttp 2244 34216 "GET /api/schema/ HTTP/1.1" 500 105335
INFO 2025-05-01 14:17:14,896 autoreload 2244 38196 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-05-01 14:17:15,678 autoreload 16032 16748 Watching for file changes with StatReloader
INFO 2025-05-01 14:17:25,295 basehttp 16032 33164 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-05-01 14:17:25,568 basehttp 16032 33164 "GET /api/schema/ HTTP/1.1" 200 45751
WARNING 2025-05-01 14:32:53,019 log 16032 32064 Unauthorized: /api/token/
WARNING 2025-05-01 14:32:53,021 basehttp 16032 32064 "POST /api/token/ HTTP/1.1" 401 172
WARNING 2025-05-01 14:33:38,630 log 16032 32064 Bad Request: /api/token/
WARNING 2025-05-01 14:33:38,632 basehttp 16032 32064 "POST /api/token/ HTTP/1.1" 400 40
ERROR 2025-05-01 14:33:57,530 log 16032 32064 Internal Server Error: /api/token/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Python312\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\rest_framework\authtoken\views.py", line 58, in post
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
ERROR 2025-05-01 14:33:57,538 basehttp 16032 32064 "POST /api/token/ HTTP/1.1" 500 102204
INFO 2025-05-01 14:38:54,734 autoreload 16032 16748 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-01 14:38:55,805 autoreload 39412 13932 Watching for file changes with StatReloader
INFO 2025-05-01 14:40:26,779 autoreload 36284 14884 Watching for file changes with StatReloader
INFO 2025-05-01 14:40:33,283 basehttp 36284 39324 "POST /api/token/ HTTP/1.1" 200 52
INFO 2025-05-01 14:46:21,571 autoreload 36284 14884 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-05-01 14:46:22,457 autoreload 35656 24900 Watching for file changes with StatReloader
WARNING 2025-05-01 14:47:44,511 log 35656 3000 Bad Request: /api/token/
WARNING 2025-05-01 14:47:44,514 basehttp 35656 3000 "POST /api/token/ HTTP/1.1" 400 37
INFO 2025-05-01 14:49:16,814 basehttp 35656 11516 "POST /api/token/ HTTP/1.1" 200 483
WARNING 2025-05-01 14:50:03,283 log 35656 11516 Unauthorized: /api/properties/properties/
WARNING 2025-05-01 14:50:03,284 basehttp 35656 11516 "GET /api/properties/properties/ HTTP/1.1" 401 172
INFO 2025-05-01 14:50:46,233 basehttp 35656 11516 "GET /api/properties/properties/ HTTP/1.1" 200 2680
WARNING 2025-05-02 08:37:40,848 log 39596 28036 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 08:37:41,239 log 39596 28036 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 08:37:41,327 log 39596 28036 Not Found: /auth/signup/
WARNING 2025-05-02 08:37:41,330 log 39596 28036 Not Found: /auth/signup/
WARNING 2025-05-02 08:37:41,718 log 39596 28036 Not Found: /auth/signup/
WARNING 2025-05-02 08:37:41,724 log 39596 28036 Not Found: /auth/signup/
WARNING 2025-05-02 08:37:41,727 log 39596 28036 Not Found: /auth/signup/
WARNING 2025-05-02 08:37:41,729 log 39596 28036 Not Found: /auth/signup/
ERROR 2025-05-02 08:55:53,088 log 1900 16136 Internal Server Error: /auth/verify-email/fake-uid/fake-token/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'web'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 86, in get
    return redirect('web:signin')
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\shortcuts.py", line 49, in redirect
    return redirect_class(resolve_url(to, *args, **kwargs))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\shortcuts.py", line 180, in resolve_url
    return reverse(to, args=args, kwargs=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'web' is not a registered namespace
ERROR 2025-05-02 08:55:53,575 log 1900 16136 Internal Server Error: /auth/verify-email/fake-uid/fake-token/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'web'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 86, in get
    return redirect('web:signin')
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\shortcuts.py", line 49, in redirect
    return redirect_class(resolve_url(to, *args, **kwargs))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\shortcuts.py", line 180, in resolve_url
    return reverse(to, args=args, kwargs=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'web' is not a registered namespace
ERROR 2025-05-02 08:55:54,086 log 1900 16136 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 64, in form_valid
    EmailService.send_welcome_email(user)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'EmailService' has no attribute 'send_welcome_email'
ERROR 2025-05-02 08:55:54,104 log 1900 16136 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/auth/signup.html
ERROR 2025-05-02 08:55:54,504 log 1900 16136 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/auth/signup.html
ERROR 2025-05-02 08:55:54,525 log 1900 16136 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/auth/signup.html
ERROR 2025-05-02 08:55:54,531 log 1900 16136 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/auth/signup.html
ERROR 2025-05-02 08:55:54,942 log 1900 16136 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 64, in form_valid
    EmailService.send_welcome_email(user)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'EmailService' has no attribute 'send_welcome_email'
WARNING 2025-05-02 08:55:59,859 log 1900 16136 Unauthorized: /api/auth/verify-email/
WARNING 2025-05-02 08:56:00,252 log 1900 16136 Unauthorized: /api/auth/verify-email/
WARNING 2025-05-02 08:56:00,664 log 1900 16136 Unauthorized: /api/auth/verify-email/
WARNING 2025-05-02 09:10:26,591 log 16532 37972 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:10:26,976 log 16532 37972 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:10:27,040 log 16532 37972 Not Found: /auth/signup/
WARNING 2025-05-02 09:10:27,043 log 16532 37972 Not Found: /auth/signup/
WARNING 2025-05-02 09:10:27,444 log 16532 37972 Not Found: /auth/signup/
WARNING 2025-05-02 09:10:27,450 log 16532 37972 Not Found: /auth/signup/
WARNING 2025-05-02 09:10:27,454 log 16532 37972 Not Found: /auth/signup/
WARNING 2025-05-02 09:10:27,456 log 16532 37972 Not Found: /auth/signup/
WARNING 2025-05-02 09:10:32,349 log 16532 37972 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:10:32,739 log 16532 37972 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:10:33,129 log 16532 37972 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:14:01,043 log 35356 36568 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:14:01,429 log 35356 36568 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:14:01,605 log 35356 36568 Not Found: /auth/signup/
WARNING 2025-05-02 09:14:01,607 log 35356 36568 Not Found: /auth/signup/
WARNING 2025-05-02 09:14:02,054 log 35356 36568 Not Found: /auth/signup/
WARNING 2025-05-02 09:14:02,062 log 35356 36568 Not Found: /auth/signup/
WARNING 2025-05-02 09:14:02,065 log 35356 36568 Not Found: /auth/signup/
WARNING 2025-05-02 09:14:02,070 log 35356 36568 Not Found: /auth/signup/
WARNING 2025-05-02 09:14:06,927 log 35356 36568 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:14:07,305 log 35356 36568 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:14:07,689 log 35356 36568 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:20:38,305 log 21456 36004 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:20:38,701 log 21456 36004 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:20:38,758 log 21456 36004 Not Found: /auth/signup/
WARNING 2025-05-02 09:20:38,763 log 21456 36004 Not Found: /auth/signup/
WARNING 2025-05-02 09:20:39,167 log 21456 36004 Not Found: /auth/signup/
WARNING 2025-05-02 09:20:39,173 log 21456 36004 Not Found: /auth/signup/
WARNING 2025-05-02 09:20:39,175 log 21456 36004 Not Found: /auth/signup/
WARNING 2025-05-02 09:20:39,177 log 21456 36004 Not Found: /auth/signup/
WARNING 2025-05-02 09:20:44,030 log 21456 36004 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:20:44,420 log 21456 36004 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:20:44,803 log 21456 36004 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:22:55,564 log 22928 36580 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 09:22:55,949 log 22928 36580 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:22:56,021 log 22928 36580 Not Found: /auth/signup/
WARNING 2025-05-02 09:22:56,025 log 22928 36580 Not Found: /auth/signup/
WARNING 2025-05-02 09:22:56,403 log 22928 36580 Not Found: /auth/signup/
WARNING 2025-05-02 09:22:56,408 log 22928 36580 Not Found: /auth/signup/
WARNING 2025-05-02 09:22:56,411 log 22928 36580 Not Found: /auth/signup/
WARNING 2025-05-02 09:22:56,416 log 22928 36580 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:01,250 log 22928 36580 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:23:01,650 log 22928 36580 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:23:02,036 log 22928 36580 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:23:37,720 log 17496 36380 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 09:23:38,131 log 17496 36380 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:23:38,196 log 17496 36380 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:38,199 log 17496 36380 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:38,600 log 17496 36380 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:38,605 log 17496 36380 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:38,607 log 17496 36380 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:38,609 log 17496 36380 Not Found: /auth/signup/
WARNING 2025-05-02 09:23:43,426 log 17496 36380 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:23:43,806 log 17496 36380 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:23:44,185 log 17496 36380 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:25:36,610 log 37024 13116 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:25:36,668 log 37024 13116 Not Found: /auth/signup/
WARNING 2025-05-02 09:25:36,670 log 37024 13116 Not Found: /auth/signup/
WARNING 2025-05-02 09:25:37,107 log 37024 13116 Not Found: /auth/signup/
WARNING 2025-05-02 09:25:37,112 log 37024 13116 Not Found: /auth/signup/
WARNING 2025-05-02 09:25:37,114 log 37024 13116 Not Found: /auth/signup/
WARNING 2025-05-02 09:25:37,116 log 37024 13116 Not Found: /auth/signup/
WARNING 2025-05-02 09:25:42,011 log 37024 13116 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:25:42,422 log 37024 13116 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:25:42,845 log 37024 13116 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:28:14,776 log 19012 39380 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 09:28:15,162 log 19012 39380 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:28:15,221 log 19012 39380 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:15,225 log 19012 39380 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:15,605 log 19012 39380 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:15,611 log 19012 39380 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:15,615 log 19012 39380 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:15,620 log 19012 39380 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:20,401 log 19012 39380 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:28:20,786 log 19012 39380 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:28:21,160 log 19012 39380 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:28:57,331 log 12116 35752 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 09:28:57,737 log 12116 35752 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:28:57,811 log 12116 35752 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:57,814 log 12116 35752 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:58,236 log 12116 35752 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:58,242 log 12116 35752 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:58,246 log 12116 35752 Not Found: /auth/signup/
WARNING 2025-05-02 09:28:58,248 log 12116 35752 Not Found: /auth/signup/
WARNING 2025-05-02 09:29:03,093 log 12116 35752 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:29:03,474 log 12116 35752 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:29:03,862 log 12116 35752 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:56:52,340 log 18924 37956 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 09:56:52,734 log 18924 37956 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 09:56:52,791 log 18924 37956 Not Found: /auth/signup/
WARNING 2025-05-02 09:56:52,794 log 18924 37956 Not Found: /auth/signup/
WARNING 2025-05-02 09:56:53,207 log 18924 37956 Not Found: /auth/signup/
WARNING 2025-05-02 09:56:53,207 log 18924 37956 Not Found: /auth/signup/
WARNING 2025-05-02 09:56:53,221 log 18924 37956 Not Found: /auth/signup/
WARNING 2025-05-02 09:56:53,225 log 18924 37956 Not Found: /auth/signup/
WARNING 2025-05-02 09:56:58,008 log 18924 37956 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:56:58,402 log 18924 37956 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 09:56:58,793 log 18924 37956 Not Found: /api/auth/verify-email/
ERROR 2025-05-02 12:02:02,608 log 14884 31408 Internal Server Error: /auth/auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/auth/signin.html
WARNING 2025-05-02 12:02:03,086 log 14884 31408 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 12:02:03,170 log 14884 31408 Not Found: /auth/signup/
WARNING 2025-05-02 12:02:03,172 log 14884 31408 Not Found: /auth/signup/
WARNING 2025-05-02 12:02:03,578 log 14884 31408 Not Found: /auth/signup/
WARNING 2025-05-02 12:02:03,585 log 14884 31408 Not Found: /auth/signup/
WARNING 2025-05-02 12:02:03,589 log 14884 31408 Not Found: /auth/signup/
WARNING 2025-05-02 12:02:03,593 log 14884 31408 Not Found: /auth/signup/
WARNING 2025-05-02 12:02:09,261 log 14884 31408 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:02:09,758 log 14884 31408 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:02:10,258 log 14884 31408 Not Found: /api/auth/verify-email/
ERROR 2025-05-02 12:08:34,602 log 10720 34372 Internal Server Error: /auth/auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_list' not found. 'user_list' is not a valid view function or pattern name.
WARNING 2025-05-02 12:08:35,216 log 10720 34372 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 12:08:35,276 log 10720 34372 Not Found: /auth/signup/
WARNING 2025-05-02 12:08:35,279 log 10720 34372 Not Found: /auth/signup/
WARNING 2025-05-02 12:08:35,684 log 10720 34372 Not Found: /auth/signup/
WARNING 2025-05-02 12:08:35,692 log 10720 34372 Not Found: /auth/signup/
WARNING 2025-05-02 12:08:35,696 log 10720 34372 Not Found: /auth/signup/
WARNING 2025-05-02 12:08:35,699 log 10720 34372 Not Found: /auth/signup/
WARNING 2025-05-02 12:08:40,759 log 10720 34372 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:08:41,199 log 10720 34372 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:08:41,591 log 10720 34372 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:15:49,725 log 31980 13572 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 12:15:50,129 log 31980 13572 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 12:15:50,185 log 31980 13572 Not Found: /auth/signup/
WARNING 2025-05-02 12:15:50,187 log 31980 13572 Not Found: /auth/signup/
WARNING 2025-05-02 12:15:50,585 log 31980 13572 Not Found: /auth/signup/
WARNING 2025-05-02 12:15:50,591 log 31980 13572 Not Found: /auth/signup/
WARNING 2025-05-02 12:15:50,597 log 31980 13572 Not Found: /auth/signup/
WARNING 2025-05-02 12:15:50,602 log 31980 13572 Not Found: /auth/signup/
WARNING 2025-05-02 12:15:56,314 log 31980 13572 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:15:56,805 log 31980 13572 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:15:57,195 log 31980 13572 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:17:59,720 log 30128 39592 Not Found: /auth/verify-email/MQ/fake-token/
WARNING 2025-05-02 12:18:00,118 log 30128 39592 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 12:18:00,172 log 30128 39592 Not Found: /auth/signup/
WARNING 2025-05-02 12:18:00,175 log 30128 39592 Not Found: /auth/signup/
WARNING 2025-05-02 12:18:00,580 log 30128 39592 Not Found: /auth/signup/
WARNING 2025-05-02 12:18:00,587 log 30128 39592 Not Found: /auth/signup/
WARNING 2025-05-02 12:18:00,590 log 30128 39592 Not Found: /auth/signup/
WARNING 2025-05-02 12:18:00,595 log 30128 39592 Not Found: /auth/signup/
WARNING 2025-05-02 12:18:05,978 log 30128 39592 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:18:06,396 log 30128 39592 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:18:06,811 log 30128 39592 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:19:03,992 log 25712 18068 Not Found: /webauth/verify-email/MQ/fake-token/
WARNING 2025-05-02 12:19:04,454 log 25712 18068 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 12:19:04,521 log 25712 18068 Not Found: /auth/signup/
WARNING 2025-05-02 12:19:04,527 log 25712 18068 Not Found: /auth/signup/
WARNING 2025-05-02 12:19:05,004 log 25712 18068 Not Found: /auth/signup/
WARNING 2025-05-02 12:19:05,010 log 25712 18068 Not Found: /auth/signup/
WARNING 2025-05-02 12:19:05,012 log 25712 18068 Not Found: /auth/signup/
WARNING 2025-05-02 12:19:05,019 log 25712 18068 Not Found: /auth/signup/
WARNING 2025-05-02 12:19:10,792 log 25712 18068 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:19:11,250 log 25712 18068 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 12:19:11,721 log 25712 18068 Not Found: /api/auth/verify-email/
INFO 2025-05-02 16:54:41,521 autoreload 21252 1936 Watching for file changes with StatReloader
INFO 2025-05-02 16:56:28,933 basehttp 21252 36660 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-05-02 16:56:29,853 basehttp 21252 36660 "GET /api/schema/ HTTP/1.1" 200 102934
WARNING 2025-05-02 16:58:26,226 log 21252 36660 Not Found: /web/signin
WARNING 2025-05-02 16:58:26,227 basehttp 21252 36660 "GET /web/signin HTTP/1.1" 404 4797
WARNING 2025-05-02 16:58:26,374 log 21252 36660 Not Found: /favicon.ico
WARNING 2025-05-02 16:58:26,377 basehttp 21252 36660 "GET /favicon.ico HTTP/1.1" 404 4800
WARNING 2025-05-02 17:00:39,295 log 21252 36660 Not Found: /web/web/signin
WARNING 2025-05-02 17:00:39,299 basehttp 21252 36660 "GET /web/web/signin HTTP/1.1" 404 4809
WARNING 2025-05-02 17:01:20,666 log 21252 36660 Not Found: /web/auth/signin
WARNING 2025-05-02 17:01:20,667 basehttp 21252 36660 "GET /web/auth/signin HTTP/1.1" 404 4812
INFO 2025-05-02 17:01:44,013 basehttp 21252 36660 "GET /auth/signin HTTP/1.1" 301 0
ERROR 2025-05-02 17:01:44,930 log 21252 34548 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_list' not found. 'user_list' is not a valid view function or pattern name.
ERROR 2025-05-02 17:01:44,943 basehttp 21252 34548 "GET /auth/signin/ HTTP/1.1" 500 141856
ERROR 2025-05-02 17:08:20,229 log 21252 22104 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user-list' not found. 'user-list' is not a valid view function or pattern name.
ERROR 2025-05-02 17:08:20,232 basehttp 21252 22104 "GET /auth/signin/ HTTP/1.1" 500 141856
INFO 2025-05-02 17:18:52,120 autoreload 39872 32404 Watching for file changes with StatReloader
ERROR 2025-05-02 17:18:58,461 log 39872 39516 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user-list' not found. 'user-list' is not a valid view function or pattern name.
ERROR 2025-05-02 17:18:58,465 basehttp 39872 39516 "GET /auth/signin/ HTTP/1.1" 500 141856
ERROR 2025-05-02 17:19:18,941 log 39872 39516 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'property_list' not found. 'property_list' is not a valid view function or pattern name.
ERROR 2025-05-02 17:19:18,944 basehttp 39872 39516 "GET /auth/signin/ HTTP/1.1" 500 141278
INFO 2025-05-02 17:34:21,174 autoreload 39872 32404 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-02 17:34:22,132 autoreload 33716 18296 Watching for file changes with StatReloader
INFO 2025-05-02 17:36:37,923 autoreload 33716 18296 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-05-02 17:36:38,792 autoreload 39636 13028 Watching for file changes with StatReloader
INFO 2025-05-02 17:39:40,396 autoreload 39636 13028 C:\Local\Projects\avensus\Broadleaf\backend\properties\web_views.py changed, reloading.
INFO 2025-05-02 17:39:41,352 autoreload 38788 39384 Watching for file changes with StatReloader
INFO 2025-05-02 17:41:28,431 autoreload 38788 39384 C:\Local\Projects\avensus\Broadleaf\backend\users\permissions.py changed, reloading.
INFO 2025-05-02 17:41:29,203 autoreload 40404 28344 Watching for file changes with StatReloader
ERROR 2025-05-02 17:42:09,450 log 40404 30244 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'property_list' not found. 'property_list' is not a valid view function or pattern name.
ERROR 2025-05-02 17:42:09,458 basehttp 40404 30244 "GET /auth/signin/ HTTP/1.1" 500 141278
ERROR 2025-05-02 17:42:54,506 log 40404 30244 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 17:42:54,509 basehttp 40404 30244 "GET /auth/signin/ HTTP/1.1" 500 142498
INFO 2025-05-02 17:46:37,709 autoreload 40404 28344 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-02 17:46:38,745 autoreload 35040 34248 Watching for file changes with StatReloader
ERROR 2025-05-02 17:47:18,211 log 35040 4868 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 17:47:18,215 basehttp 35040 4868 "GET /auth/signin/ HTTP/1.1" 500 142498
INFO 2025-05-02 18:08:53,437 autoreload 35040 34248 C:\Local\Projects\avensus\Broadleaf\backend\properties\web_urls.py changed, reloading.
INFO 2025-05-02 18:08:54,462 autoreload 32888 4200 Watching for file changes with StatReloader
INFO 2025-05-02 18:09:31,863 autoreload 32888 4200 C:\Local\Projects\avensus\Broadleaf\backend\users\permissions.py changed, reloading.
INFO 2025-05-02 18:09:32,838 autoreload 31316 38760 Watching for file changes with StatReloader
ERROR 2025-05-02 18:11:22,380 log 31316 36668 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 18:11:22,391 basehttp 31316 36668 "GET /auth/signin/ HTTP/1.1" 500 142498
INFO 2025-05-02 18:22:44,322 autoreload 31316 38760 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-02 18:22:45,108 autoreload 29768 33216 Watching for file changes with StatReloader
ERROR 2025-05-02 18:22:51,481 log 29768 18924 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 18:22:51,485 basehttp 29768 18924 "GET /auth/signin/ HTTP/1.1" 500 142498
INFO 2025-05-02 18:39:08,485 autoreload 40508 18344 Watching for file changes with StatReloader
ERROR 2025-05-02 18:39:16,067 log 40508 5756 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 18:39:16,074 basehttp 40508 5756 "GET /auth/signin/ HTTP/1.1" 500 142498
ERROR 2025-05-02 18:44:24,774 log 40508 39816 Internal Server Error: /auth/signin/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 18:44:24,779 basehttp 40508 39816 "GET /auth/signin/ HTTP/1.1" 500 142498
INFO 2025-05-02 18:53:38,859 basehttp 40508 30644 "GET /auth/signin/ HTTP/1.1" 200 3145
WARNING 2025-05-02 18:53:38,931 basehttp 40508 30644 "GET /static/web/css/style.css HTTP/1.1" 404 1865
WARNING 2025-05-02 18:53:38,942 basehttp 40508 5660 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-02 18:55:06,098 autoreload 40508 18344 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-02 18:55:07,517 autoreload 19888 31868 Watching for file changes with StatReloader
INFO 2025-05-02 18:55:21,350 basehttp 19888 38972 "GET /auth/signin/ HTTP/1.1" 200 3145
WARNING 2025-05-02 18:55:21,420 basehttp 19888 31340 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-02 18:55:21,602 basehttp 19888 38972 "GET /static/web/css/style.css HTTP/1.1" 200 1
INFO 2025-05-02 18:58:51,084 basehttp 19888 10320 "GET /properties/ HTTP/1.1" 302 0
INFO 2025-05-02 18:58:51,109 basehttp 19888 10320 "GET /auth/signin/?next=/properties/ HTTP/1.1" 200 3145
WARNING 2025-05-02 18:58:51,219 basehttp 19888 10320 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-02 18:58:54,806 basehttp 19888 22228 "GET /users/ HTTP/1.1" 302 0
INFO 2025-05-02 18:58:54,815 basehttp 19888 22228 "GET /auth/signin/?next=/users/ HTTP/1.1" 200 3145
WARNING 2025-05-02 18:58:54,918 basehttp 19888 22228 "GET /static/web/js/main.js HTTP/1.1" 404 1856
WARNING 2025-05-02 18:59:12,222 log 33056 38576 Not Found: /webauth/verify-email/MQ/fake-token/
WARNING 2025-05-02 18:59:12,792 log 33056 38576 Not Found: /auth/verify-email/fake-uid/fake-token/
WARNING 2025-05-02 18:59:12,853 log 33056 38576 Not Found: /auth/signup/
WARNING 2025-05-02 18:59:12,856 log 33056 38576 Not Found: /auth/signup/
WARNING 2025-05-02 18:59:13,270 log 33056 38576 Not Found: /auth/signup/
WARNING 2025-05-02 18:59:13,276 log 33056 38576 Not Found: /auth/signup/
WARNING 2025-05-02 18:59:13,280 log 33056 38576 Not Found: /auth/signup/
WARNING 2025-05-02 18:59:13,286 log 33056 38576 Not Found: /auth/signup/
WARNING 2025-05-02 18:59:18,227 log 33056 38576 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 18:59:18,631 log 33056 38576 Not Found: /api/auth/verify-email/
WARNING 2025-05-02 18:59:19,024 log 33056 38576 Not Found: /api/auth/verify-email/
INFO 2025-05-02 19:03:45,173 basehttp 19888 17312 "POST /auth/signin/?next=/users/ HTTP/1.1" 200 3333
WARNING 2025-05-02 19:03:45,219 basehttp 19888 17312 "GET /static/web/js/main.js HTTP/1.1" 404 1856
ERROR 2025-05-02 19:06:07,130 log 19888 37604 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 71, in reverse
    extra, resolver = resolver.namespace_dict[ns]
                      ~~~~~~~~~~~~~~~~~~~~~~~^^^^
KeyError: 'properties'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 82, in reverse
    raise NoReverseMatch("%s is not a registered namespace" % key)
django.urls.exceptions.NoReverseMatch: 'properties' is not a registered namespace
ERROR 2025-05-02 19:06:07,142 basehttp 19888 37604 "GET /auth/signup/ HTTP/1.1" 500 157564
INFO 2025-05-02 19:07:38,046 autoreload 19888 31868 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-02 19:07:38,848 autoreload 39872 25796 Watching for file changes with StatReloader
INFO 2025-05-02 19:12:16,848 basehttp 39872 6800 "GET /auth/signup/ HTTP/1.1" 200 4414
WARNING 2025-05-02 19:12:16,916 basehttp 39872 6800 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-02 19:19:03,072 basehttp 39872 32496 "POST /auth/signup/ HTTP/1.1" 200 4758
WARNING 2025-05-02 19:19:03,153 basehttp 39872 32496 "GET /static/web/js/main.js HTTP/1.1" 404 1856
ERROR 2025-05-02 19:19:30,387 log 39872 38496 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 63, in form_valid
    EmailService.send_verification_email(user, self.request)
  File "C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py", line 40, in send_verification_email
    send_mail(
  File "C:\Python312\Lib\site-packages\django\core\mail\__init__.py", line 88, in send_mail
    return mail.send()
           ^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\mail\message.py", line 301, in send
    return self.get_connection(fail_silently).send_messages([self])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\mail\backends\smtp.py", line 136, in send_messages
    sent = self._send(message)
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\mail\backends\smtp.py", line 155, in _send
    self.connection.sendmail(
  File "C:\Python312\Lib\smtplib.py", line 876, in sendmail
    raise SMTPSenderRefused(code, resp, from_addr)
smtplib.SMTPSenderRefused: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/mail/?p=WantAuthError 5b1f17b1804b1-441ae3fb8fesm86461685e9.1 - gsmtp', '<EMAIL>')
ERROR 2025-05-02 19:19:30,400 basehttp 39872 38496 "POST /auth/signup/ HTTP/1.1" 500 122252
INFO 2025-05-02 19:25:35,010 basehttp 39872 35752 "GET /auth/signin/ HTTP/1.1" 200 3145
WARNING 2025-05-02 19:25:35,096 basehttp 39872 35752 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-03 08:46:38,434 autoreload 39872 25796 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-03 08:46:39,719 autoreload 36432 38180 Watching for file changes with StatReloader
INFO 2025-05-03 09:01:20,825 autoreload 36432 38180 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-03 09:01:22,106 autoreload 20108 9516 Watching for file changes with StatReloader
INFO 2025-05-03 09:02:55,400 autoreload 20108 9516 C:\Local\Projects\avensus\Broadleaf\backend\users\apps.py changed, reloading.
INFO 2025-05-03 09:02:56,349 autoreload 39964 36148 Watching for file changes with StatReloader
INFO 2025-05-03 09:06:04,471 autoreload 39964 36148 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-03 09:06:05,480 autoreload 25192 31584 Watching for file changes with StatReloader
INFO 2025-05-03 09:13:19,778 basehttp 25192 5684 "GET /auth/signin/ HTTP/1.1" 200 3145
INFO 2025-05-03 09:13:19,929 basehttp 25192 5684 "GET /static/web/css/style.css HTTP/1.1" 304 0
WARNING 2025-05-03 09:13:19,974 basehttp 25192 5684 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-03 09:30:24,065 basehttp 25192 37268 "GET /auth/signup/ HTTP/1.1" 200 4414
WARNING 2025-05-03 09:30:24,229 basehttp 25192 37268 "GET /static/web/js/main.js HTTP/1.1" 404 1856
ERROR 2025-05-03 09:31:09,567 log 25192 34320 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\views.py", line 61, in form_valid
    user = User.objects.create_user(**user_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\contrib\auth\models.py", line 162, in create_user
    return self._create_user(username, email, password, **extra_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\contrib\auth\models.py", line 156, in _create_user
    user.save(using=self._db)
  File "C:\Local\Projects\avensus\Broadleaf\backend\users\models.py", line 50, in save
    super().save(*args, **kwargs)
  File "C:\Python312\Lib\site-packages\django\contrib\auth\base_user.py", line 62, in save
    super().save(*args, **kwargs)
  File "C:\Python312\Lib\site-packages\django\db\models\base.py", line 892, in save
    self.save_base(
  File "C:\Python312\Lib\site-packages\django\db\models\base.py", line 1013, in save_base
    post_save.send(
  File "C:\Python312\Lib\site-packages\django\dispatch\dispatcher.py", line 189, in send
    response = receiver(signal=self, sender=sender, **named)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\users\signals.py", line 11, in queue_user_emails
    EmailService.queue_verification_email(instance)
  File "C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py", line 20, in queue_verification_email
    {verification_link}
     ^^^^^^^^^^^^^^^^^
NameError: name 'verification_link' is not defined
ERROR 2025-05-03 09:31:09,583 basehttp 25192 34320 "POST /auth/signup/ HTTP/1.1" 500 130979
INFO 2025-05-03 09:53:03,452 autoreload 25192 31584 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-03 09:53:04,679 autoreload 16868 39856 Watching for file changes with StatReloader
INFO 2025-05-03 09:54:27,965 autoreload 16868 39856 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-03 09:54:28,667 autoreload 39848 22628 Watching for file changes with StatReloader
INFO 2025-05-03 09:56:32,428 autoreload 39848 22628 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-03 09:56:33,704 autoreload 37856 5756 Watching for file changes with StatReloader
INFO 2025-05-03 09:58:29,832 autoreload 37856 5756 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-03 09:58:30,793 autoreload 28932 15652 Watching for file changes with StatReloader
INFO 2025-05-03 09:59:14,868 autoreload 28932 15652 C:\Local\Projects\avensus\Broadleaf\backend\core\models.py changed, reloading.
INFO 2025-05-03 09:59:15,811 autoreload 27840 22660 Watching for file changes with StatReloader
INFO 2025-05-03 10:04:26,546 basehttp 27840 40456 "POST /auth/signup/ HTTP/1.1" 200 4972
WARNING 2025-05-03 10:04:26,608 basehttp 27840 40456 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-03 10:16:43,531 basehttp 27840 30360 "POST /auth/signup/ HTTP/1.1" 200 4721
WARNING 2025-05-03 10:16:43,596 basehttp 27840 30360 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-03 10:17:01,498 basehttp 27840 32996 "POST /auth/signup/ HTTP/1.1" 302 0
INFO 2025-05-03 10:17:01,533 basehttp 27840 32996 "GET /auth/signin/ HTTP/1.1" 200 3695
WARNING 2025-05-03 10:17:01,596 basehttp 27840 32996 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-04 09:25:05,663 autoreload 27840 22660 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-04 09:25:08,005 autoreload 5288 29896 Watching for file changes with StatReloader
INFO 2025-05-04 09:26:59,773 autoreload 5288 29896 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-05-04 09:27:00,655 autoreload 34276 13880 Watching for file changes with StatReloader
INFO 2025-05-04 09:34:56,486 autoreload 34276 13880 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-04 09:34:57,421 autoreload 34508 24256 Watching for file changes with StatReloader
INFO 2025-05-04 09:35:10,128 autoreload 34508 24256 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-04 09:35:11,095 autoreload 9448 28772 Watching for file changes with StatReloader
INFO 2025-05-04 09:35:50,384 basehttp 9448 6828 "GET /auth/signin/ HTTP/1.1" 200 3145
INFO 2025-05-04 09:35:50,571 basehttp 9448 6828 "GET /static/web/css/style.css HTTP/1.1" 304 0
WARNING 2025-05-04 09:35:50,625 basehttp 9448 18356 "GET /static/web/js/main.js HTTP/1.1" 404 1856
INFO 2025-05-04 09:41:05,924 autoreload 36172 37476 Watching for file changes with StatReloader
INFO 2025-05-04 09:41:13,018 basehttp 36172 14800 "GET /auth/signin/ HTTP/1.1" 200 5484
INFO 2025-05-04 09:41:13,343 basehttp 36172 14800 "GET /static/web/css/footer.css HTTP/1.1" 200 383
INFO 2025-05-04 09:41:13,361 basehttp 36172 37372 "GET /static/web/css/signin.css HTTP/1.1" 200 765
INFO 2025-05-04 09:41:13,363 basehttp 36172 20840 "GET /static/web/css/navbar.css HTTP/1.1" 200 2303
INFO 2025-05-04 09:41:13,367 basehttp 36172 14800 "GET /static/web/img/Broadleaf.png HTTP/1.1" 200 18525
INFO 2025-05-04 09:41:37,846 basehttp 36172 14800 "GET /auth/signup/ HTTP/1.1" 200 5789
INFO 2025-05-04 09:41:37,891 basehttp 36172 14800 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-04 09:41:37,899 basehttp 36172 20840 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-04 09:41:37,899 basehttp 36172 37372 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-04 09:41:37,909 basehttp 36172 14800 "GET /static/web/css/signup.css HTTP/1.1" 200 766
INFO 2025-05-04 15:35:50,287 autoreload 36172 37476 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-04 15:35:52,794 autoreload 26188 30792 Watching for file changes with StatReloader
INFO 2025-05-04 15:36:03,877 autoreload 26188 30792 C:\Local\Projects\avensus\Broadleaf\backend\properties\forms.py changed, reloading.
INFO 2025-05-04 15:36:05,296 autoreload 14460 31400 Watching for file changes with StatReloader
INFO 2025-05-04 15:36:18,148 autoreload 14460 31400 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-04 15:36:19,461 autoreload 22520 36588 Watching for file changes with StatReloader
INFO 2025-05-04 15:40:04,906 autoreload 22520 36588 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-04 15:40:06,304 autoreload 39664 11776 Watching for file changes with StatReloader
INFO 2025-05-04 15:44:29,507 autoreload 39664 11776 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-04 15:44:30,704 autoreload 40116 22304 Watching for file changes with StatReloader
INFO 2025-05-04 15:44:41,606 autoreload 40116 22304 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-04 15:44:42,782 autoreload 6684 34340 Watching for file changes with StatReloader
INFO 2025-05-04 15:52:55,186 autoreload 6684 34340 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-04 15:52:56,359 autoreload 12032 31524 Watching for file changes with StatReloader
INFO 2025-05-04 15:57:28,293 autoreload 40796 23884 Watching for file changes with StatReloader
INFO 2025-05-04 16:00:05,796 basehttp 40796 32356 "GET / HTTP/1.1" 200 4318
INFO 2025-05-04 16:00:05,944 basehttp 40796 32356 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-04 16:00:05,970 basehttp 40796 29712 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-04 16:00:05,971 basehttp 40796 37708 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
WARNING 2025-05-04 16:00:06,026 basehttp 40796 32356 "GET /static/web/css/home.css HTTP/1.1" 404 1862
WARNING 2025-05-04 16:00:20,075 log 40796 37708 Not Found: /property.html
WARNING 2025-05-04 16:00:20,080 basehttp 40796 37708 "GET /property.html HTTP/1.1" 404 9035
ERROR 2025-05-04 16:06:52,379 log 40796 40552 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'about' not found. 'about' is not a valid view function or pattern name.
ERROR 2025-05-04 16:06:52,397 basehttp 40796 40552 "GET / HTTP/1.1" 500 142757
INFO 2025-05-04 16:07:10,874 autoreload 40796 23884 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-05-04 16:07:12,008 autoreload 19300 32268 Watching for file changes with StatReloader
INFO 2025-05-04 16:08:31,024 autoreload 19300 32268 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-05-04 16:08:32,416 autoreload 36412 10592 Watching for file changes with StatReloader
INFO 2025-05-04 16:08:34,812 autoreload 36412 10592 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-04 16:08:36,041 autoreload 33756 22176 Watching for file changes with StatReloader
ERROR 2025-05-04 16:08:47,733 log 33756 34216 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'blog' not found. 'blog' is not a valid view function or pattern name.
ERROR 2025-05-04 16:08:47,744 basehttp 33756 34216 "GET / HTTP/1.1" 500 141727
ERROR 2025-05-04 16:09:16,100 log 33756 34216 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'blog' not found. 'blog' is not a valid view function or pattern name.
ERROR 2025-05-04 16:09:16,106 basehttp 33756 34216 "GET / HTTP/1.1" 500 142302
ERROR 2025-05-04 16:09:26,980 log 33756 34216 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'blog' not found. 'blog' is not a valid view function or pattern name.
ERROR 2025-05-04 16:09:26,989 basehttp 33756 34216 "GET / HTTP/1.1" 500 142165
ERROR 2025-05-04 16:09:44,777 log 33756 34216 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\base.py", line 969, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'services' not found. 'services' is not a valid view function or pattern name.
ERROR 2025-05-04 16:09:44,777 basehttp 33756 34216 "GET / HTTP/1.1" 500 142056
INFO 2025-05-04 16:10:17,530 basehttp 33756 34216 "GET / HTTP/1.1" 200 4152
WARNING 2025-05-04 16:10:17,583 basehttp 33756 34216 "GET /static/web/css/home.css HTTP/1.1" 404 1862
INFO 2025-05-04 16:10:20,892 basehttp 33756 4788 "GET /about/ HTTP/1.1" 200 6018
WARNING 2025-05-04 16:10:21,025 basehttp 33756 4788 "GET /static/web/css/about.css HTTP/1.1" 404 1865
WARNING 2025-05-04 16:10:21,187 basehttp 33756 31972 "GET /static/web/img/about-image.jpg HTTP/1.1" 404 1883
INFO 2025-05-04 16:10:28,124 basehttp 33756 36296 "GET /properties/ HTTP/1.1" 200 7589
INFO 2025-05-04 16:10:49,532 basehttp 33756 36296 "GET /contact/ HTTP/1.1" 200 7250
WARNING 2025-05-04 16:10:49,623 basehttp 33756 36296 "GET /static/web/css/contact.css HTTP/1.1" 404 1871
INFO 2025-05-05 23:26:01,765 autoreload 33756 22176 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-05 23:26:04,715 autoreload 20184 34212 Watching for file changes with StatReloader
INFO 2025-05-05 23:27:07,317 autoreload 20184 34212 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-05-05 23:27:08,775 autoreload 40968 30264 Watching for file changes with StatReloader
INFO 2025-05-05 23:34:02,871 autoreload 40968 30264 C:\Local\Projects\avensus\Broadleaf\backend\properties\storage.py changed, reloading.
INFO 2025-05-05 23:34:04,454 autoreload 3596 16700 Watching for file changes with StatReloader
INFO 2025-05-05 23:34:24,858 autoreload 3596 16700 C:\Local\Projects\avensus\Broadleaf\backend\properties\storage.py changed, reloading.
INFO 2025-05-05 23:34:25,954 autoreload 19584 26196 Watching for file changes with StatReloader
INFO 2025-05-05 23:34:43,157 autoreload 19584 26196 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-05 23:34:44,555 autoreload 13288 35096 Watching for file changes with StatReloader
INFO 2025-05-05 23:40:00,058 autoreload 13288 35096 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-05 23:40:01,205 autoreload 13344 25344 Watching for file changes with StatReloader
INFO 2025-05-05 23:41:08,571 autoreload 13344 25344 C:\Local\Projects\avensus\Broadleaf\backend\properties\storage.py changed, reloading.
INFO 2025-05-05 23:41:10,046 autoreload 35936 12232 Watching for file changes with StatReloader
INFO 2025-05-05 23:41:25,399 autoreload 35936 12232 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-05 23:41:26,690 autoreload 34248 37560 Watching for file changes with StatReloader
INFO 2025-05-05 23:44:55,146 autoreload 34248 37560 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-05 23:44:56,539 autoreload 19580 30724 Watching for file changes with StatReloader
INFO 2025-05-05 23:46:02,697 autoreload 19580 30724 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-05 23:46:03,864 autoreload 37200 15728 Watching for file changes with StatReloader
INFO 2025-05-05 23:52:34,261 autoreload 37200 15728 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-05-05 23:52:35,338 autoreload 7304 18644 Watching for file changes with StatReloader
INFO 2025-05-05 23:54:29,402 basehttp 7304 30936 "GET /auth/signin/ HTTP/1.1" 200 5318
INFO 2025-05-05 23:54:29,587 basehttp 7304 30936 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-05 23:54:29,587 basehttp 7304 41436 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-05 23:54:29,587 basehttp 7304 32160 "GET /static/web/css/signin.css HTTP/1.1" 304 0
INFO 2025-05-05 23:54:29,598 basehttp 7304 30936 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-05 23:54:33,942 basehttp 7304 30936 "GET / HTTP/1.1" 200 15157
INFO 2025-05-05 23:54:34,797 basehttp 7304 30936 "GET /static/web/img/Cover.png HTTP/1.1" 200 225390
INFO 2025-05-05 23:54:34,798 basehttp 7304 41436 "GET /static/web/img/Screenshot3.png HTTP/1.1" 200 199902
INFO 2025-05-05 23:54:34,802 basehttp 7304 40488 "GET /static/web/css/home.css HTTP/1.1" 200 7880
INFO 2025-05-05 23:54:34,813 basehttp 7304 32160 "GET /static/web/img/Screenshot1.png HTTP/1.1" 200 237291
INFO 2025-05-05 23:54:34,827 basehttp 7304 30936 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 200 5608
INFO 2025-05-05 23:54:34,834 basehttp 7304 41436 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 200 7044
INFO 2025-05-05 23:54:34,841 basehttp 7304 40488 "GET /static/web/img/Google.png HTTP/1.1" 200 8451
INFO 2025-05-05 23:54:34,847 basehttp 7304 32160 "GET /static/web/img/Apple.png HTTP/1.1" 200 8490
INFO 2025-05-05 23:54:34,895 basehttp 7304 30936 "GET /static/web/img/app%20download.png HTTP/1.1" 200 296858
INFO 2025-05-05 23:54:34,971 basehttp 7304 40488 "GET /static/web/img/about.png HTTP/1.1" 200 1681290
INFO 2025-05-05 23:54:34,978 basehttp 7304 41436 "GET /static/web/img/landing-image.png HTTP/1.1" 200 2091106
INFO 2025-05-05 23:54:35,034 basehttp 7304 41256 "GET /static/web/img/Profile.png HTTP/1.1" 200 8670
INFO 2025-05-05 23:54:35,048 basehttp 7304 1016 "GET /static/web/img/Search.svg HTTP/1.1" 200 483
INFO 2025-05-05 23:54:53,029 basehttp 7304 1016 "GET /contact/ HTTP/1.1" 200 7250
WARNING 2025-05-05 23:54:53,124 basehttp 7304 1016 "GET /static/web/css/contact.css HTTP/1.1" 404 1871
INFO 2025-05-05 23:55:23,772 basehttp 7304 41256 "GET /properties/ HTTP/1.1" 200 8373
INFO 2025-05-05 23:55:23,907 basehttp 7304 41256 "GET /static/web/css/propertylist.css HTTP/1.1" 200 683
INFO 2025-05-05 23:55:23,912 basehttp 7304 41436 "GET /static/web/img/Image-3.png HTTP/1.1" 200 192226
INFO 2025-05-05 23:55:23,987 basehttp 7304 41256 "GET /static/web/img/woman.png HTTP/1.1" 200 1597292
INFO 2025-05-07 08:07:25,686 autoreload 7304 18644 C:\Local\Projects\avensus\Broadleaf\backend\users\views.py changed, reloading.
INFO 2025-05-07 08:07:27,968 autoreload 41080 33908 Watching for file changes with StatReloader
INFO 2025-05-07 08:19:04,533 autoreload 41080 33908 C:\Local\Projects\avensus\Broadleaf\backend\users\migrations\0001_initial.py changed, reloading.
INFO 2025-05-07 08:19:05,846 autoreload 39072 1388 Watching for file changes with StatReloader
INFO 2025-05-07 08:19:22,016 autoreload 39072 1388 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-07 08:19:23,601 autoreload 15932 1868 Watching for file changes with StatReloader
INFO 2025-05-07 08:19:36,698 autoreload 15932 1868 C:\Local\Projects\avensus\Broadleaf\backend\users\models.py changed, reloading.
INFO 2025-05-07 08:19:38,079 autoreload 40596 33420 Watching for file changes with StatReloader
INFO 2025-05-07 08:21:15,367 autoreload 40596 33420 C:\Local\Projects\avensus\Broadleaf\backend\users\apps.py changed, reloading.
INFO 2025-05-07 08:24:36,603 autoreload 1868 16812 Watching for file changes with StatReloader
INFO 2025-05-07 08:25:17,288 basehttp 1868 22284 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-05-07 08:25:17,893 basehttp 1868 22284 "GET /api/schema/ HTTP/1.1" 200 102495
INFO 2025-05-07 08:25:22,839 basehttp 1868 22284 "GET / HTTP/1.1" 200 15157
INFO 2025-05-07 08:25:23,457 basehttp 1868 3284 "GET /static/web/css/home.css HTTP/1.1" 200 7880
INFO 2025-05-07 08:25:23,468 basehttp 1868 22284 "GET /static/web/css/footer.css HTTP/1.1" 200 383
INFO 2025-05-07 08:25:23,476 basehttp 1868 20348 "GET /static/web/css/navbar.css HTTP/1.1" 200 2303
INFO 2025-05-07 08:25:23,540 basehttp 1868 20348 "GET /static/web/img/Cover.png HTTP/1.1" 200 225390
INFO 2025-05-07 08:25:23,545 basehttp 1868 22284 "GET /static/web/img/Screenshot1.png HTTP/1.1" 200 237291
INFO 2025-05-07 08:25:23,577 basehttp 1868 22284 "GET /static/web/img/Profile.png HTTP/1.1" 200 8670
INFO 2025-05-07 08:25:23,585 basehttp 1868 20348 "GET /static/web/img/Screenshot3.png HTTP/1.1" 200 199902
INFO 2025-05-07 08:25:23,600 basehttp 1868 22284 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 200 5608
INFO 2025-05-07 08:25:23,608 basehttp 1868 20348 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 200 7044
INFO 2025-05-07 08:25:23,631 basehttp 1868 22284 "GET /static/web/img/Google.png HTTP/1.1" 200 8451
INFO 2025-05-07 08:25:23,740 basehttp 1868 20348 "GET /static/web/img/landing-image.png HTTP/1.1" 200 2091106
INFO 2025-05-07 08:25:23,740 basehttp 1868 4004 "GET /static/web/img/Broadleaf.png HTTP/1.1" 200 18525
INFO 2025-05-07 08:25:23,744 basehttp 1868 36912 "GET /static/web/img/Search.svg HTTP/1.1" 200 483
INFO 2025-05-07 08:25:23,773 basehttp 1868 20348 "GET /static/web/img/Apple.png HTTP/1.1" 200 8490
INFO 2025-05-07 08:25:23,798 basehttp 1868 22284 "GET /static/web/img/app%20download.png HTTP/1.1" 200 296858
INFO 2025-05-07 08:25:23,894 basehttp 1868 4004 "GET /static/web/img/about.png HTTP/1.1" 200 1681290
WARNING 2025-05-07 08:25:23,967 log 1868 4004 Not Found: /favicon.ico
WARNING 2025-05-07 08:25:23,969 basehttp 1868 4004 "GET /favicon.ico HTTP/1.1" 404 9539
INFO 2025-05-07 08:25:27,538 basehttp 1868 4004 "GET /auth/signin/ HTTP/1.1" 200 5318
INFO 2025-05-07 08:25:27,646 basehttp 1868 4004 "GET /static/web/css/signin.css HTTP/1.1" 200 765
INFO 2025-05-07 08:25:34,381 basehttp 1868 4004 "GET /properties/ HTTP/1.1" 200 8373
INFO 2025-05-07 08:25:34,444 basehttp 1868 4004 "GET /static/web/css/propertylist.css HTTP/1.1" 200 683
INFO 2025-05-07 08:25:34,453 basehttp 1868 22284 "GET /static/web/img/Image-3.png HTTP/1.1" 200 192226
INFO 2025-05-07 08:25:34,541 basehttp 1868 22284 "GET /static/web/img/woman.png HTTP/1.1" 200 1597292
INFO 2025-05-07 08:25:44,144 basehttp 1868 22284 "GET /contact/ HTTP/1.1" 200 7250
WARNING 2025-05-07 08:25:44,204 basehttp 1868 22284 "GET /static/web/css/contact.css HTTP/1.1" 404 1871
INFO 2025-05-07 08:25:47,238 basehttp 1868 4004 "GET /about/ HTTP/1.1" 200 10322
INFO 2025-05-07 08:25:47,321 basehttp 1868 4004 "GET /static/web/css/about.css HTTP/1.1" 200 3890
INFO 2025-05-07 08:25:47,389 basehttp 1868 20348 "GET /static/web/img/spouses.png HTTP/1.1" 200 1001670
INFO 2025-05-07 08:25:47,426 basehttp 1868 4004 "GET /static/web/img/about%20us%20landing%20page.png HTTP/1.1" 200 2064323
INFO 2025-05-08 14:11:27,025 autoreload 7360 17992 Watching for file changes with StatReloader
INFO 2025-05-08 14:17:11,530 autoreload 26448 19880 Watching for file changes with StatReloader
INFO 2025-05-08 17:53:19,598 autoreload 32164 31676 Watching for file changes with StatReloader
INFO 2025-05-10 00:20:12,521 autoreload 1020 1668 Watching for file changes with StatReloader
INFO 2025-05-10 00:20:58,806 autoreload 16936 9928 Watching for file changes with StatReloader
INFO 2025-05-10 00:22:01,959 basehttp 16936 2584 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-05-10 00:22:04,189 basehttp 16936 2584 "GET /api/schema/ HTTP/1.1" 200 139358
INFO 2025-05-10 00:22:57,387 basehttp 16936 2584 "GET / HTTP/1.1" 200 15157
INFO 2025-05-10 00:22:57,465 basehttp 16936 2584 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,465 basehttp 16936 14160 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,473 basehttp 16936 21628 "GET /static/web/css/home.css HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,487 basehttp 16936 14160 "GET /static/web/img/Screenshot1.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,487 basehttp 16936 21628 "GET /static/web/img/Cover.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,489 basehttp 16936 2584 "GET /static/web/img/Profile.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,494 basehttp 16936 21628 "GET /static/web/img/Screenshot3.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,494 basehttp 16936 14160 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,496 basehttp 16936 2584 "GET /static/web/img/Search.svg HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,501 basehttp 16936 21628 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,507 basehttp 16936 14160 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,509 basehttp 16936 2584 "GET /static/web/img/Google.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,511 basehttp 16936 21628 "GET /static/web/img/Apple.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,513 basehttp 16936 14160 "GET /static/web/img/app%20download.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,513 basehttp 16936 2584 "GET /static/web/img/landing-image.png HTTP/1.1" 304 0
INFO 2025-05-10 00:22:57,513 basehttp 16936 21628 "GET /static/web/img/about.png HTTP/1.1" 304 0
WARNING 2025-05-10 00:22:57,728 log 16936 21628 Not Found: /favicon.ico
WARNING 2025-05-10 00:22:57,728 basehttp 16936 21628 "GET /favicon.ico HTTP/1.1" 404 15148
INFO 2025-05-10 00:23:14,724 basehttp 16936 21628 "GET /about/ HTTP/1.1" 200 10322
INFO 2025-05-10 00:23:14,957 basehttp 16936 21628 "GET /static/web/css/about.css HTTP/1.1" 200 3890
INFO 2025-05-10 00:23:14,982 basehttp 16936 14160 "GET /static/web/img/woman.png HTTP/1.1" 304 0
INFO 2025-05-10 00:23:15,039 basehttp 16936 2584 "GET /static/web/img/spouses.png HTTP/1.1" 200 1001670
INFO 2025-05-10 00:23:15,085 basehttp 16936 21628 "GET /static/web/img/about%20us%20landing%20page.png HTTP/1.1" 200 2064323
INFO 2025-05-10 00:23:17,302 basehttp 16936 21628 "GET /properties/ HTTP/1.1" 200 6223
INFO 2025-05-10 00:23:17,350 basehttp 16936 21628 "GET /static/web/css/propertylist.css HTTP/1.1" 304 0
INFO 2025-05-10 00:23:17,373 basehttp 16936 21628 "GET /static/web/img/Image-3.png HTTP/1.1" 304 0
INFO 2025-05-10 00:23:27,541 basehttp 16936 21628 "GET /contact/ HTTP/1.1" 200 7250
WARNING 2025-05-10 00:23:27,608 basehttp 16936 21628 "GET /static/web/css/contact.css HTTP/1.1" 404 1997
INFO 2025-05-10 10:45:16,395 autoreload 16936 9928 C:\Local\Projects\avensus\Broadleaf\backend\users\forms.py changed, reloading.
INFO 2025-05-10 10:45:17,880 autoreload 18100 20032 Watching for file changes with StatReloader
INFO 2025-05-10 10:45:25,446 autoreload 18100 20032 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-05-10 10:45:26,649 autoreload 30588 20280 Watching for file changes with StatReloader
INFO 2025-05-10 10:48:56,459 autoreload 30588 20280 C:\Local\Projects\avensus\Broadleaf\backend\properties\admin.py changed, reloading.
INFO 2025-05-10 10:48:57,648 autoreload 16284 21524 Watching for file changes with StatReloader
INFO 2025-05-10 11:10:53,836 autoreload 16284 21524 C:\Local\Projects\avensus\Broadleaf\backend\users\models.py changed, reloading.
INFO 2025-05-10 11:10:54,989 autoreload 24656 11600 Watching for file changes with StatReloader
INFO 2025-05-10 11:12:00,318 autoreload 24656 11600 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-10 11:12:02,497 autoreload 7360 14508 Watching for file changes with StatReloader
INFO 2025-05-10 11:20:02,853 autoreload 7360 14508 C:\Local\Projects\avensus\Broadleaf\backend\backend\urls.py changed, reloading.
INFO 2025-05-10 11:20:03,765 autoreload 11592 32360 Watching for file changes with StatReloader
INFO 2025-05-10 11:20:11,810 autoreload 11592 32360 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-10 11:20:13,397 autoreload 31028 29792 Watching for file changes with StatReloader
INFO 2025-05-13 08:22:44,674 autoreload 31028 29792 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-13 08:22:47,744 autoreload 6484 32140 Watching for file changes with StatReloader
INFO 2025-05-13 08:37:34,934 basehttp 6484 13140 "GET / HTTP/1.1" 200 15157
INFO 2025-05-13 08:37:35,010 basehttp 6484 22208 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,010 basehttp 6484 13140 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,015 basehttp 6484 22208 "GET /static/web/css/home.css HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,027 basehttp 6484 22208 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,027 basehttp 6484 13140 "GET /static/web/img/Cover.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,033 basehttp 6484 13140 "GET /static/web/img/Screenshot1.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,038 basehttp 6484 22208 "GET /static/web/img/Screenshot3.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,038 basehttp 6484 26732 "GET /static/web/img/Search.svg HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,038 basehttp 6484 13140 "GET /static/web/img/Profile.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,060 basehttp 6484 13140 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,083 basehttp 6484 26732 "GET /static/web/img/Google.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,083 basehttp 6484 22208 "GET /static/web/img/Apple.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,083 basehttp 6484 13140 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,083 basehttp 6484 26732 "GET /static/web/img/app%20download.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,098 basehttp 6484 22208 "GET /static/web/img/landing-image.png HTTP/1.1" 304 0
INFO 2025-05-13 08:37:35,098 basehttp 6484 13140 "GET /static/web/img/about.png HTTP/1.1" 304 0
INFO 2025-05-13 08:39:07,515 basehttp 6484 13140 "GET /auth/signin/ HTTP/1.1" 200 5375
INFO 2025-05-13 08:39:07,567 basehttp 6484 13140 "GET /static/web/css/signin.css HTTP/1.1" 304 0
INFO 2025-05-13 08:39:19,861 basehttp 6484 13140 "POST /auth/signin/ HTTP/1.1" 200 5389
INFO 2025-05-13 08:39:47,255 basehttp 6484 13140 "GET /about/ HTTP/1.1" 200 10322
INFO 2025-05-13 08:39:47,318 basehttp 6484 13140 "GET /static/web/css/about.css HTTP/1.1" 304 0
INFO 2025-05-13 08:39:47,338 basehttp 6484 22208 "GET /static/web/img/spouses.png HTTP/1.1" 304 0
INFO 2025-05-13 08:39:47,338 basehttp 6484 26732 "GET /static/web/img/woman.png HTTP/1.1" 304 0
INFO 2025-05-13 08:39:47,338 basehttp 6484 13140 "GET /static/web/img/about%20us%20landing%20page.png HTTP/1.1" 304 0
INFO 2025-05-13 08:39:51,452 basehttp 6484 13140 "GET / HTTP/1.1" 200 15157
INFO 2025-05-13 08:39:53,916 basehttp 6484 13140 "GET /auth/signin/ HTTP/1.1" 200 5375
INFO 2025-05-13 08:40:08,019 basehttp 6484 13140 "POST /auth/signin/ HTTP/1.1" 200 5389
INFO 2025-05-13 08:58:30,472 basehttp 6484 22320 "GET /properties/ HTTP/1.1" 200 6232
INFO 2025-05-13 08:58:30,569 basehttp 6484 22320 "GET /static/web/css/propertylist.css HTTP/1.1" 304 0
INFO 2025-05-13 10:23:22,874 autoreload 6484 32140 C:\Local\Projects\avensus\Broadleaf\backend\properties\forms.py changed, reloading.
INFO 2025-05-13 10:23:24,824 autoreload 24136 13868 Watching for file changes with StatReloader
INFO 2025-05-13 10:25:29,778 autoreload 24136 13868 C:\Local\Projects\avensus\Broadleaf\backend\properties\admin.py changed, reloading.
INFO 2025-05-13 10:25:31,397 autoreload 29280 27912 Watching for file changes with StatReloader
INFO 2025-05-13 10:29:42,557 autoreload 29280 27912 C:\Local\Projects\avensus\Broadleaf\backend\web\admin_views.py changed, reloading.
INFO 2025-05-13 10:29:44,476 autoreload 33732 27432 Watching for file changes with StatReloader
INFO 2025-05-13 10:36:33,377 autoreload 33732 27432 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-13 10:36:35,155 autoreload 8516 23744 Watching for file changes with StatReloader
INFO 2025-05-13 10:41:56,900 autoreload 8516 23744 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-13 10:41:58,863 autoreload 11184 6388 Watching for file changes with StatReloader
INFO 2025-05-13 10:43:45,626 autoreload 11184 6388 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-13 10:43:47,296 autoreload 7660 29288 Watching for file changes with StatReloader
INFO 2025-05-13 10:49:17,940 autoreload 7660 29288 C:\Local\Projects\avensus\Broadleaf\backend\web\admin_views.py changed, reloading.
INFO 2025-05-13 10:49:19,717 autoreload 31780 27052 Watching for file changes with StatReloader
INFO 2025-05-13 10:49:51,703 autoreload 31780 27052 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-13 10:49:53,137 autoreload 32124 25540 Watching for file changes with StatReloader
INFO 2025-05-13 11:01:15,392 autoreload 32124 25540 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-13 11:01:16,900 autoreload 28660 25936 Watching for file changes with StatReloader
INFO 2025-05-13 11:20:40,815 autoreload 28660 25936 C:\Local\Projects\avensus\Broadleaf\backend\web\admin_views.py changed, reloading.
INFO 2025-05-13 11:20:42,658 autoreload 9648 13508 Watching for file changes with StatReloader
INFO 2025-05-13 11:21:45,649 autoreload 9648 13508 C:\Local\Projects\avensus\Broadleaf\backend\web\admin_views.py changed, reloading.
INFO 2025-05-13 11:21:47,162 autoreload 20140 18336 Watching for file changes with StatReloader
INFO 2025-05-14 00:33:37,091 autoreload 20140 18336 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-14 00:33:40,997 autoreload 6728 4712 Watching for file changes with StatReloader
INFO 2025-05-14 00:49:23,027 autoreload 6728 4712 C:\Local\Projects\avensus\Broadleaf\backend\users\models.py changed, reloading.
INFO 2025-05-14 00:49:24,919 autoreload 20384 30076 Watching for file changes with StatReloader
INFO 2025-05-14 00:49:46,488 autoreload 20384 30076 C:\Local\Projects\avensus\Broadleaf\backend\web\user_views.py changed, reloading.
INFO 2025-05-14 00:49:48,372 autoreload 27624 9992 Watching for file changes with StatReloader
INFO 2025-05-14 00:50:14,533 autoreload 27624 9992 C:\Local\Projects\avensus\Broadleaf\backend\web\user_views.py changed, reloading.
INFO 2025-05-14 00:50:16,429 autoreload 9328 32660 Watching for file changes with StatReloader
INFO 2025-05-14 00:50:46,868 autoreload 9328 32660 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-14 00:50:48,935 autoreload 5252 22832 Watching for file changes with StatReloader
INFO 2025-05-14 23:54:29,746 autoreload 27928 25012 Watching for file changes with StatReloader
INFO 2025-05-15 00:03:42,445 autoreload 13568 2928 Watching for file changes with StatReloader
INFO 2025-05-15 00:12:24,262 basehttp 13568 5436 "GET / HTTP/1.1" 200 15157
INFO 2025-05-15 00:12:24,394 basehttp 13568 19032 "GET /static/web/css/navbar.css HTTP/1.1" 200 2303
INFO 2025-05-15 00:12:24,396 basehttp 13568 5436 "GET /static/web/css/footer.css HTTP/1.1" 200 383
INFO 2025-05-15 00:12:24,398 basehttp 13568 19032 "GET /static/web/css/home.css HTTP/1.1" 200 7880
INFO 2025-05-15 00:12:24,399 basehttp 13568 5436 "GET /static/web/img/Broadleaf.png HTTP/1.1" 200 18525
INFO 2025-05-15 00:12:24,406 basehttp 13568 19032 "GET /static/web/img/Cover.png HTTP/1.1" 200 225390
INFO 2025-05-15 00:12:24,406 basehttp 13568 5436 "GET /static/web/img/Screenshot1.png HTTP/1.1" 200 237291
INFO 2025-05-15 00:12:24,411 basehttp 13568 5436 "GET /static/web/img/Search.svg HTTP/1.1" 200 483
INFO 2025-05-15 00:12:24,415 basehttp 13568 5436 "GET /static/web/img/Profile.png HTTP/1.1" 200 8670
INFO 2025-05-15 00:12:24,417 basehttp 13568 19032 "GET /static/web/img/Screenshot3.png HTTP/1.1" 200 199902
INFO 2025-05-15 00:12:24,419 basehttp 13568 5436 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 200 5608
INFO 2025-05-15 00:12:24,420 basehttp 13568 19032 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 200 7044
INFO 2025-05-15 00:12:24,423 basehttp 13568 5436 "GET /static/web/img/Google.png HTTP/1.1" 200 8451
INFO 2025-05-15 00:12:24,456 basehttp 13568 19032 "GET /static/web/img/Apple.png HTTP/1.1" 200 8490
INFO 2025-05-15 00:12:24,460 basehttp 13568 5436 "GET /static/web/img/app%20download.png HTTP/1.1" 200 296858
INFO 2025-05-15 00:12:24,649 basehttp 13568 19032 "GET /static/web/img/about.png HTTP/1.1" 200 1681290
INFO 2025-05-15 00:12:24,652 basehttp 13568 5436 "GET /static/web/img/landing-image.png HTTP/1.1" 200 2091106
WARNING 2025-05-15 00:12:24,715 log 13568 5436 Not Found: /favicon.ico
WARNING 2025-05-15 00:12:24,716 basehttp 13568 5436 "GET /favicon.ico HTTP/1.1" 404 18267
INFO 2025-05-15 00:22:18,880 basehttp 13568 6968 "GET /auth/signin/ HTTP/1.1" 200 5375
INFO 2025-05-15 00:22:18,967 basehttp 13568 6968 "GET /static/web/css/signin.css HTTP/1.1" 200 765
INFO 2025-05-15 00:23:19,802 basehttp 13568 6968 "POST /auth/signin/ HTTP/1.1" 302 0
INFO 2025-05-15 00:23:19,959 basehttp 13568 6968 "GET /dashboard/ HTTP/1.1" 200 36652
INFO 2025-05-15 00:23:20,047 basehttp 13568 6968 "GET /static/web/plugins/perfect-scrollbar/css/perfect-scrollbar.css HTTP/1.1" 200 1819
INFO 2025-05-15 00:23:20,057 basehttp 13568 6968 "GET /static/web/plugins/metismenu/metisMenu.min.css HTTP/1.1" 200 1945
INFO 2025-05-15 00:23:20,063 basehttp 13568 6968 "GET /static/web/plugins/metismenu/mm-vertical.css HTTP/1.1" 200 1056
INFO 2025-05-15 00:23:20,072 basehttp 13568 6968 "GET /static/web/plugins/simplebar/css/simplebar.css HTTP/1.1" 200 2964
INFO 2025-05-15 00:23:20,092 basehttp 13568 6968 "GET /static/web/css/bootstrap.min.css HTTP/1.1" 200 232808
INFO 2025-05-15 00:23:20,098 basehttp 13568 6968 "GET /static/web/css/bootstrap-extended.css HTTP/1.1" 200 15843
INFO 2025-05-15 00:23:20,104 basehttp 13568 6968 "GET /static/web/css/sass/main.css HTTP/1.1" 200 24351
INFO 2025-05-15 00:23:20,113 basehttp 13568 6968 "GET /static/web/css/sass/dark-theme.css HTTP/1.1" 200 10639
INFO 2025-05-15 00:23:20,119 basehttp 13568 6968 "GET /static/web/css/sass/semi-dark.css HTTP/1.1" 200 3960
INFO 2025-05-15 00:23:20,124 basehttp 13568 6968 "GET /static/web/css/sass/bordered-theme.css HTTP/1.1" 200 458
INFO 2025-05-15 00:23:20,132 basehttp 13568 6968 "GET /static/web/css/sass/responsive.css HTTP/1.1" 200 1853
INFO 2025-05-15 00:23:20,142 basehttp 13568 6968 "GET /static/web/js/bootstrap.bundle.min.js HTTP/1.1" 200 80727
INFO 2025-05-15 00:23:20,344 basehttp 13568 6968 "GET /static/web/img/apps/01.png HTTP/1.1" 200 7267
INFO 2025-05-15 00:23:20,352 basehttp 13568 25100 "GET /static/web/img/apps/02.png HTTP/1.1" 200 22269
INFO 2025-05-15 00:23:20,352 basehttp 13568 6968 "GET /static/web/img/apps/03.png HTTP/1.1" 200 18096
INFO 2025-05-15 00:23:20,368 basehttp 13568 25100 "GET /static/web/img/apps/04.png HTTP/1.1" 200 8618
INFO 2025-05-15 00:23:20,373 basehttp 13568 6968 "GET /static/web/img/apps/05.png HTTP/1.1" 200 42155
INFO 2025-05-15 00:23:20,382 basehttp 13568 25100 "GET /static/web/js/jquery.min.js HTTP/1.1" 200 89503
INFO 2025-05-15 00:23:20,382 basehttp 13568 6968 "GET /static/web/plugins/perfect-scrollbar/js/perfect-scrollbar.js HTTP/1.1" 200 26482
INFO 2025-05-15 00:23:20,392 basehttp 13568 25100 "GET /static/web/plugins/metismenu/metisMenu.min.js HTTP/1.1" 200 4767
INFO 2025-05-15 00:23:20,402 basehttp 13568 25100 "GET /static/web/js/index.js HTTP/1.1" 200 7829
INFO 2025-05-15 00:23:20,412 basehttp 13568 25100 "GET /static/web/plugins/peity/jquery.peity.min.js HTTP/1.1" 200 3722
INFO 2025-05-15 00:23:20,424 basehttp 13568 25100 "GET /static/web/plugins/simplebar/js/simplebar.min.js HTTP/1.1" 200 58025
INFO 2025-05-15 00:23:20,432 basehttp 13568 25100 "GET /static/web/js/main.js HTTP/1.1" 200 2436
INFO 2025-05-15 00:23:20,437 basehttp 13568 6968 "GET /static/web/plugins/apexchart/apexcharts.min.js HTTP/1.1" 200 512623
INFO 2025-05-15 00:23:20,442 basehttp 13568 25100 "GET /static/web/img/avatars/01.png HTTP/1.1" 200 51939
INFO 2025-05-15 00:23:20,452 basehttp 13568 6968 "GET /static/web/img/avatars/02.png HTTP/1.1" 200 54575
INFO 2025-05-15 00:23:20,462 basehttp 13568 25100 "GET /static/web/img/avatars/03.png HTTP/1.1" 200 43156
INFO 2025-05-15 00:23:20,477 basehttp 13568 25100 "GET /static/web/img/apps/07.png HTTP/1.1" 200 13588
INFO 2025-05-15 00:23:20,483 basehttp 13568 6968 "GET /static/web/img/apps/06.png HTTP/1.1" 200 49935
INFO 2025-05-15 00:23:20,499 basehttp 13568 6968 "GET /static/web/img/apps/08.png HTTP/1.1" 200 51110
INFO 2025-05-15 00:23:20,501 basehttp 13568 25100 "GET /static/web/img/apps/09.png HTTP/1.1" 200 15600
INFO 2025-05-15 00:23:20,509 basehttp 13568 25100 "GET /static/web/img/apps/11.png HTTP/1.1" 200 12556
INFO 2025-05-15 00:23:20,512 basehttp 13568 6968 "GET /static/web/img/apps/10.png HTTP/1.1" 200 13804
INFO 2025-05-15 00:23:20,524 basehttp 13568 6968 "GET /static/web/img/apps/12.png HTTP/1.1" 200 14516
INFO 2025-05-15 00:23:20,524 basehttp 13568 25100 "GET /static/web/img/county/02.png HTTP/1.1" 200 16194
INFO 2025-05-15 00:23:20,534 basehttp 13568 6968 "GET /static/web/img/county/01.png HTTP/1.1" 200 13296
INFO 2025-05-15 00:23:20,534 basehttp 13568 25100 "GET /static/web/img/county/03.png HTTP/1.1" 200 7881
INFO 2025-05-15 00:23:20,542 basehttp 13568 25100 "GET /static/web/img/county/04.png HTTP/1.1" 200 10347
INFO 2025-05-15 00:23:20,545 basehttp 13568 6968 "GET /static/web/img/county/05.png HTTP/1.1" 200 28830
INFO 2025-05-15 00:23:20,556 basehttp 13568 25100 "GET /static/web/img/county/06.png HTTP/1.1" 200 22668
INFO 2025-05-15 00:23:20,556 basehttp 13568 6968 "GET /static/web/img/county/07.png HTTP/1.1" 200 16949
INFO 2025-05-15 00:23:20,566 basehttp 13568 25100 "GET /static/web/img/county/08.png HTTP/1.1" 200 22060
WARNING 2025-05-15 00:23:21,680 log 13568 25100 Not Found: /dashboard/assets/images/favicon-32x32.png
WARNING 2025-05-15 00:23:21,682 basehttp 13568 25100 "GET /dashboard/assets/images/favicon-32x32.png HTTP/1.1" 404 18357
INFO 2025-05-15 00:23:38,932 basehttp 13568 25100 "GET /dashboard/users/ HTTP/1.1" 200 46206
WARNING 2025-05-15 00:23:39,136 log 13568 25100 Not Found: /dashboard/users/assets/images/favicon-32x32.png
WARNING 2025-05-15 00:23:39,141 basehttp 13568 25100 "GET /dashboard/users/assets/images/favicon-32x32.png HTTP/1.1" 404 18375
INFO 2025-05-15 00:24:35,387 basehttp 13568 25100 "GET /users/1/ HTTP/1.1" 200 36613
WARNING 2025-05-15 00:24:35,547 log 13568 25100 Not Found: /users/1/assets/images/favicon-32x32.png
WARNING 2025-05-15 00:24:35,550 basehttp 13568 25100 "GET /users/1/assets/images/favicon-32x32.png HTTP/1.1" 404 18351
INFO 2025-05-15 00:25:10,851 basehttp 13568 25100 "GET /dashboard/users/?page=2 HTTP/1.1" 200 37752
INFO 2025-05-15 00:25:19,457 basehttp 13568 25100 "GET /dashboard/users/?page=1 HTTP/1.1" 200 46206
INFO 2025-05-15 00:25:46,792 basehttp 13568 25100 "GET /dashboard/users/?search=admin%40atzambia.com HTTP/1.1" 200 37192
INFO 2025-05-15 00:25:57,738 basehttp 13568 25100 "GET /dashboard/ HTTP/1.1" 200 36652
INFO 2025-05-15 00:25:59,937 basehttp 13568 25100 "GET /dashboard/users/ HTTP/1.1" 200 46206
INFO 2025-05-15 00:26:29,502 basehttp 13568 25100 "GET /dashboard/users/create/ HTTP/1.1" 200 35995
WARNING 2025-05-15 00:26:29,608 log 13568 25100 Not Found: /dashboard/users/create/assets/images/favicon-32x32.png
WARNING 2025-05-15 00:26:29,612 basehttp 13568 25100 "GET /dashboard/users/create/assets/images/favicon-32x32.png HTTP/1.1" 404 18396
INFO 2025-05-15 00:27:12,866 basehttp 13568 25100 "GET /dashboard/users/ HTTP/1.1" 200 46206
INFO 2025-05-15 00:27:15,756 basehttp 13568 25100 "GET /users/1/update/ HTTP/1.1" 200 35834
WARNING 2025-05-15 00:27:15,947 log 13568 25100 Not Found: /users/1/update/assets/images/favicon-32x32.png
WARNING 2025-05-15 00:27:15,947 basehttp 13568 25100 "GET /users/1/update/assets/images/favicon-32x32.png HTTP/1.1" 404 18372
INFO 2025-05-15 00:29:17,816 basehttp 13568 25100 "GET /dashboard/agents/ HTTP/1.1" 200 41365
WARNING 2025-05-15 00:29:17,984 log 13568 25100 Not Found: /dashboard/agents/assets/images/favicon-32x32.png
WARNING 2025-05-15 00:29:17,989 basehttp 13568 25100 "GET /dashboard/agents/assets/images/favicon-32x32.png HTTP/1.1" 404 18378
INFO 2025-05-15 23:34:01,315 autoreload 9672 24672 Watching for file changes with StatReloader
INFO 2025-05-15 23:34:37,621 basehttp 9672 14412 "GET / HTTP/1.1" 200 15157
INFO 2025-05-15 23:34:37,701 basehttp 9672 13636 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,702 basehttp 9672 24980 "GET /static/web/css/home.css HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,705 basehttp 9672 14412 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,709 basehttp 9672 13636 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,711 basehttp 9672 14412 "GET /static/web/img/Screenshot1.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,712 basehttp 9672 24980 "GET /static/web/img/Cover.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,716 basehttp 9672 13636 "GET /static/web/img/Screenshot3.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,720 basehttp 9672 24980 "GET /static/web/img/Profile.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,721 basehttp 9672 14412 "GET /static/web/img/Search.svg HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,724 basehttp 9672 13636 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,731 basehttp 9672 14412 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,733 basehttp 9672 13636 "GET /static/web/img/Apple.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,735 basehttp 9672 24980 "GET /static/web/img/Google.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,738 basehttp 9672 13636 "GET /static/web/img/landing-image.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,738 basehttp 9672 14412 "GET /static/web/img/app%20download.png HTTP/1.1" 304 0
INFO 2025-05-15 23:34:37,741 basehttp 9672 24980 "GET /static/web/img/about.png HTTP/1.1" 304 0
WARNING 2025-05-15 23:34:37,888 log 9672 24980 Not Found: /favicon.ico
WARNING 2025-05-15 23:34:37,889 basehttp 9672 24980 "GET /favicon.ico HTTP/1.1" 404 18307
INFO 2025-05-15 23:34:43,572 basehttp 9672 24980 "GET /auth/signin/ HTTP/1.1" 200 5375
INFO 2025-05-15 23:34:43,631 basehttp 9672 24980 "GET /static/web/css/signin.css HTTP/1.1" 304 0
INFO 2025-05-15 23:34:59,900 basehttp 9672 24980 "POST /auth/signin/ HTTP/1.1" 302 0
INFO 2025-05-15 23:35:00,360 basehttp 9672 24980 "GET /dashboard/ HTTP/1.1" 200 35124
INFO 2025-05-15 23:35:01,388 basehttp 9672 24980 "GET /static/web/plugins/perfect-scrollbar/css/perfect-scrollbar.css HTTP/1.1" 200 1819
INFO 2025-05-15 23:35:01,391 basehttp 9672 22492 "GET /static/web/plugins/simplebar/css/simplebar.css HTTP/1.1" 200 2964
INFO 2025-05-15 23:35:01,441 basehttp 9672 13636 "GET /static/web/plugins/metismenu/mm-vertical.css HTTP/1.1" 200 1056
INFO 2025-05-15 23:35:01,451 basehttp 9672 18640 "GET /static/web/css/sass/main.css HTTP/1.1" 200 24351
INFO 2025-05-15 23:35:01,514 basehttp 9672 22492 "GET /static/web/css/sass/dark-theme.css HTTP/1.1" 200 10639
INFO 2025-05-15 23:35:01,515 basehttp 9672 14412 "GET /static/web/plugins/metismenu/metisMenu.min.css HTTP/1.1" 200 1945
INFO 2025-05-15 23:35:01,542 basehttp 9672 18640 "GET /static/web/css/sass/bordered-theme.css HTTP/1.1" 200 458
INFO 2025-05-15 23:35:01,557 basehttp 9672 22492 "GET /static/web/css/sass/responsive.css HTTP/1.1" 200 1853
INFO 2025-05-15 23:35:01,561 basehttp 9672 13636 "GET /static/web/css/sass/semi-dark.css HTTP/1.1" 200 3960
INFO 2025-05-15 23:35:01,574 basehttp 9672 18640 "GET /static/web/img/apps/01.png HTTP/1.1" 200 7267
INFO 2025-05-15 23:35:01,631 basehttp 9672 2240 "GET /static/web/css/bootstrap.min.css HTTP/1.1" 200 232808
INFO 2025-05-15 23:35:01,637 basehttp 9672 24980 "GET /static/web/css/bootstrap-extended.css HTTP/1.1" 200 15843
INFO 2025-05-15 23:35:01,638 basehttp 9672 13636 "GET /static/web/img/apps/03.png HTTP/1.1" 200 18096
INFO 2025-05-15 23:35:01,656 basehttp 9672 22492 "GET /static/web/img/apps/02.png HTTP/1.1" 200 22269
INFO 2025-05-15 23:35:01,673 basehttp 9672 18640 "GET /static/web/img/apps/04.png HTTP/1.1" 200 8618
INFO 2025-05-15 23:35:01,743 basehttp 9672 22492 "GET /static/web/img/apps/05.png HTTP/1.1" 200 42155
INFO 2025-05-15 23:35:01,781 basehttp 9672 24980 "GET /static/web/plugins/metismenu/metisMenu.min.js HTTP/1.1" 200 4767
INFO 2025-05-15 23:35:01,782 basehttp 9672 22492 "GET /static/web/js/index.js HTTP/1.1" 200 7829
INFO 2025-05-15 23:35:01,807 basehttp 9672 22492 "GET /static/web/img/county/02.png HTTP/1.1" 200 16194
INFO 2025-05-15 23:35:01,825 basehttp 9672 24980 "GET /static/web/img/avatars/01.png HTTP/1.1" 200 51939
INFO 2025-05-15 23:35:01,863 basehttp 9672 2240 "GET /static/web/plugins/perfect-scrollbar/js/perfect-scrollbar.js HTTP/1.1" 200 26482
INFO 2025-05-15 23:35:01,935 basehttp 9672 22492 "GET /static/web/plugins/peity/jquery.peity.min.js HTTP/1.1" 200 3722
INFO 2025-05-15 23:35:01,942 basehttp 9672 13636 "GET /static/web/js/jquery.min.js HTTP/1.1" 200 89503
INFO 2025-05-15 23:35:01,968 basehttp 9672 14412 "GET /static/web/js/bootstrap.bundle.min.js HTTP/1.1" 200 80727
INFO 2025-05-15 23:35:01,981 basehttp 9672 22492 "GET /static/web/img/avatars/03.png HTTP/1.1" 200 43156
INFO 2025-05-15 23:35:01,987 basehttp 9672 13636 "GET /static/web/img/avatars/02.png HTTP/1.1" 200 54575
INFO 2025-05-15 23:35:02,006 basehttp 9672 14412 "GET /static/web/img/apps/06.png HTTP/1.1" 200 49935
INFO 2025-05-15 23:35:02,012 basehttp 9672 22492 "GET /static/web/img/apps/07.png HTTP/1.1" 200 13588
INFO 2025-05-15 23:35:02,030 basehttp 9672 13636 "GET /static/web/img/apps/08.png HTTP/1.1" 200 51110
INFO 2025-05-15 23:35:02,039 basehttp 9672 14412 "GET /static/web/img/apps/09.png HTTP/1.1" 200 15600
INFO 2025-05-15 23:35:02,040 basehttp 9672 22492 "GET /static/web/img/apps/10.png HTTP/1.1" 200 13804
INFO 2025-05-15 23:35:02,042 basehttp 9672 24980 "GET /static/web/plugins/simplebar/js/simplebar.min.js HTTP/1.1" 200 58025
INFO 2025-05-15 23:35:02,049 basehttp 9672 2240 "GET /static/web/js/main.js HTTP/1.1" 200 2436
INFO 2025-05-15 23:35:02,074 basehttp 9672 2240 "GET /static/web/img/county/04.png HTTP/1.1" 200 10347
INFO 2025-05-15 23:35:02,078 basehttp 9672 24980 "GET /static/web/img/apps/11.png HTTP/1.1" 200 12556
INFO 2025-05-15 23:35:02,085 basehttp 9672 13636 "GET /static/web/img/apps/12.png HTTP/1.1" 200 14516
INFO 2025-05-15 23:35:02,088 basehttp 9672 14412 "GET /static/web/img/county/03.png HTTP/1.1" 200 7881
INFO 2025-05-15 23:35:02,095 basehttp 9672 22492 "GET /static/web/img/county/01.png HTTP/1.1" 200 13296
INFO 2025-05-15 23:35:02,109 basehttp 9672 2240 "GET /static/web/img/county/06.png HTTP/1.1" 200 22668
INFO 2025-05-15 23:35:02,113 basehttp 9672 13636 "GET /static/web/img/county/07.png HTTP/1.1" 200 16949
INFO 2025-05-15 23:35:02,120 basehttp 9672 24980 "GET /static/web/img/county/05.png HTTP/1.1" 200 28830
INFO 2025-05-15 23:35:02,121 basehttp 9672 14412 "GET /static/web/img/county/08.png HTTP/1.1" 200 22060
INFO 2025-05-15 23:35:02,140 basehttp 9672 18640 "GET /static/web/plugins/apexchart/apexcharts.min.js HTTP/1.1" 200 512623
WARNING 2025-05-15 23:35:02,311 log 9672 18640 Not Found: /dashboard/assets/images/favicon-32x32.png
WARNING 2025-05-15 23:35:02,314 basehttp 9672 18640 "GET /dashboard/assets/images/favicon-32x32.png HTTP/1.1" 404 18397
INFO 2025-05-15 23:35:09,274 basehttp 9672 18640 "GET /dashboard/users/ HTTP/1.1" 200 44665
WARNING 2025-05-15 23:35:09,447 log 9672 18640 Not Found: /dashboard/users/assets/images/favicon-32x32.png
WARNING 2025-05-15 23:35:09,448 basehttp 9672 18640 "GET /dashboard/users/assets/images/favicon-32x32.png HTTP/1.1" 404 18415
INFO 2025-05-15 23:35:14,138 basehttp 9672 18640 "GET /dashboard/users/ HTTP/1.1" 200 44665
INFO 2025-05-15 23:35:17,106 basehttp 9672 18640 "GET /dashboard/users/ HTTP/1.1" 200 44665
INFO 2025-05-15 23:35:21,563 basehttp 9672 18640 "GET /dashboard/agents/ HTTP/1.1" 200 39824
WARNING 2025-05-15 23:35:21,664 log 9672 18640 Not Found: /dashboard/agents/assets/images/favicon-32x32.png
WARNING 2025-05-15 23:35:21,664 basehttp 9672 18640 "GET /dashboard/agents/assets/images/favicon-32x32.png HTTP/1.1" 404 18418
INFO 2025-05-15 23:35:31,595 basehttp 9672 18640 "GET /dashboard/users/create/ HTTP/1.1" 200 34454
WARNING 2025-05-15 23:35:31,710 log 9672 18640 Not Found: /dashboard/users/create/assets/images/favicon-32x32.png
WARNING 2025-05-15 23:35:31,710 basehttp 9672 18640 "GET /dashboard/users/create/assets/images/favicon-32x32.png HTTP/1.1" 404 18436
INFO 2025-05-16 15:44:39,216 autoreload 9672 24672 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-16 15:44:43,592 autoreload 19872 25320 Watching for file changes with StatReloader
INFO 2025-05-18 12:13:43,968 autoreload 8868 20236 Watching for file changes with StatReloader
INFO 2025-05-18 12:27:41,467 autoreload 10928 13032 Watching for file changes with StatReloader
INFO 2025-05-18 12:28:35,293 basehttp 10928 19952 "GET / HTTP/1.1" 200 16316
INFO 2025-05-18 12:28:35,354 basehttp 10928 20464 "GET /static/web/css/home.css HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,356 basehttp 10928 19952 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,358 basehttp 10928 20464 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,364 basehttp 10928 20464 "GET /static/web/img/Cover.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,364 basehttp 10928 19952 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,368 basehttp 10928 20464 "GET /static/web/img/Screenshot1.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,368 basehttp 10928 19952 "GET /static/web/img/Screenshot3.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,370 basehttp 10928 20464 "GET /static/web/img/Search.svg HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,370 basehttp 10928 19952 "GET /static/web/img/Profile.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,376 basehttp 10928 20464 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,378 basehttp 10928 19952 "GET /static/web/img/Google.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,382 basehttp 10928 19996 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,382 basehttp 10928 20464 "GET /static/web/img/Apple.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,386 basehttp 10928 19952 "GET /static/web/img/app%20download.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,387 basehttp 10928 19996 "GET /static/web/img/landing-image.png HTTP/1.1" 304 0
INFO 2025-05-18 12:28:35,387 basehttp 10928 19952 "GET /static/web/img/about.png HTTP/1.1" 304 0
WARNING 2025-05-18 12:28:35,493 log 10928 19952 Not Found: /favicon.ico
WARNING 2025-05-18 12:28:35,495 basehttp 10928 19952 "GET /favicon.ico HTTP/1.1" 404 18562
INFO 2025-05-18 12:28:46,662 basehttp 10928 19952 "POST /logout/ HTTP/1.1" 302 0
INFO 2025-05-18 12:28:46,670 basehttp 10928 19952 "GET / HTTP/1.1" 200 15421
INFO 2025-05-18 12:30:10,357 basehttp 10928 19952 "GET /auth/signin/ HTTP/1.1" 200 5630
INFO 2025-05-18 12:30:10,397 basehttp 10928 19952 "GET /static/web/css/signin.css HTTP/1.1" 304 0
INFO 2025-05-18 12:30:18,209 basehttp 10928 19952 "GET /auth/signup/ HTTP/1.1" 200 5930
INFO 2025-05-18 12:30:18,309 basehttp 10928 19952 "GET /static/web/css/signup.css HTTP/1.1" 200 766
INFO 2025-05-18 18:13:34,827 autoreload 10928 13032 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-05-18 18:13:36,164 autoreload 19144 19184 Watching for file changes with StatReloader
INFO 2025-05-18 18:55:38,757 basehttp 19144 10648 "GET / HTTP/1.1" 200 15064
INFO 2025-05-18 18:56:00,703 basehttp 19144 10648 "GET /properties/ HTTP/1.1" 200 6487
INFO 2025-05-18 18:56:00,943 basehttp 19144 10648 "GET /static/web/css/propertylist.css HTTP/1.1" 200 683
INFO 2025-05-18 18:56:00,979 basehttp 19144 10648 "GET /static/web/img/woman.png HTTP/1.1" 200 1597292
INFO 2025-05-20 08:49:32,037 basehttp 19144 21356 "GET / HTTP/1.1" 200 15064
INFO 2025-05-20 08:49:32,163 basehttp 19144 21356 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,165 basehttp 19144 29968 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,165 basehttp 19144 3012 "GET /static/web/css/home.css HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,174 basehttp 19144 29968 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,175 basehttp 19144 3012 "GET /static/web/img/Cover.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,179 basehttp 19144 21356 "GET /static/web/img/Profile.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,184 basehttp 19144 29968 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,184 basehttp 19144 3012 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,186 basehttp 19144 21356 "GET /static/web/img/Search.svg HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,191 basehttp 19144 29968 "GET /static/web/img/Google.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,196 basehttp 19144 3012 "GET /static/web/img/Apple.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,197 basehttp 19144 21356 "GET /static/web/img/app%20download.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,216 basehttp 19144 21356 "GET /static/web/img/landing-image.png HTTP/1.1" 304 0
INFO 2025-05-20 08:49:32,222 basehttp 19144 3012 "GET /static/web/img/about.png HTTP/1.1" 304 0
WARNING 2025-05-20 08:49:32,481 log 19144 3012 Not Found: /favicon.ico
WARNING 2025-05-20 08:49:32,490 basehttp 19144 3012 "GET /favicon.ico HTTP/1.1" 404 18562
INFO 2025-05-20 08:50:00,489 basehttp 19144 3012 "GET /auth/signin/ HTTP/1.1" 200 5630
INFO 2025-05-20 08:50:00,544 basehttp 19144 3012 "GET /static/web/css/signin.css HTTP/1.1" 304 0
INFO 2025-05-20 08:50:03,954 basehttp 19144 3012 "GET /auth/signup/ HTTP/1.1" 200 5930
INFO 2025-05-20 08:50:04,015 basehttp 19144 3012 "GET /static/web/css/signup.css HTTP/1.1" 200 766
INFO 2025-05-20 08:52:12,239 basehttp 19144 3012 "POST /auth/signup/ HTTP/1.1" 200 6189
INFO 2025-05-20 08:52:32,233 basehttp 19144 3012 "POST /auth/signup/ HTTP/1.1" 200 6146
ERROR 2025-05-20 08:52:52,602 log 19144 3012 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py", line 60, in form_valid
    user = User.objects.create_user(**user_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: CustomUserManager.create_user() missing 1 required positional argument: 'name'
ERROR 2025-05-20 08:52:52,611 basehttp 19144 3012 "POST /auth/signup/ HTTP/1.1" 500 96358
INFO 2025-05-20 09:03:23,997 basehttp 19144 32180 "POST /auth/signup/ HTTP/1.1" 200 6202
INFO 2025-05-20 09:03:29,652 basehttp 19144 32180 "GET / HTTP/1.1" 200 15064
INFO 2025-05-20 09:03:31,773 basehttp 19144 32180 "GET /auth/signin/ HTTP/1.1" 200 5630
INFO 2025-05-20 09:03:35,676 basehttp 19144 32180 "GET /auth/signup/ HTTP/1.1" 200 5986
INFO 2025-05-20 09:04:26,880 autoreload 31764 32624 Watching for file changes with StatReloader
INFO 2025-05-20 09:04:47,926 basehttp 31764 34276 "GET / HTTP/1.1" 200 15064
INFO 2025-05-20 09:04:50,483 basehttp 31764 34276 "GET /auth/signin/ HTTP/1.1" 200 5630
INFO 2025-05-20 09:04:55,109 basehttp 31764 34276 "GET /auth/signup/ HTTP/1.1" 200 5986
INFO 2025-05-20 09:07:00,035 basehttp 31764 34276 "GET /auth/signup/ HTTP/1.1" 200 6014
INFO 2025-05-20 09:07:00,418 basehttp 31764 34276 "GET /static/web/css/footer.css HTTP/1.1" 200 383
INFO 2025-05-20 09:07:00,418 basehttp 31764 20804 "GET /static/web/css/signup.css HTTP/1.1" 200 766
INFO 2025-05-20 09:07:00,420 basehttp 31764 24448 "GET /static/web/img/Broadleaf.png HTTP/1.1" 200 18525
INFO 2025-05-20 09:07:00,489 basehttp 31764 26340 "GET /static/web/css/navbar.css HTTP/1.1" 200 2303
WARNING 2025-05-20 09:07:00,913 log 31764 26340 Not Found: /favicon.ico
WARNING 2025-05-20 09:07:00,918 basehttp 31764 26340 "GET /favicon.ico HTTP/1.1" 404 18562
INFO 2025-05-20 09:11:49,162 autoreload 31764 32624 C:\Local\Projects\avensus\Broadleaf\backend\web\forms.py changed, reloading.
INFO 2025-05-20 09:11:50,365 autoreload 24956 24592 Watching for file changes with StatReloader
INFO 2025-05-20 09:12:20,230 autoreload 24956 24592 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-05-20 09:12:21,371 autoreload 32820 27304 Watching for file changes with StatReloader
INFO 2025-05-20 09:13:05,207 basehttp 32820 3192 "GET /auth/signup/ HTTP/1.1" 200 6112
INFO 2025-05-20 09:13:05,661 basehttp 32820 3024 "GET /static/web/css/signup.css HTTP/1.1" 200 766
INFO 2025-05-20 09:13:05,666 basehttp 32820 28280 "GET /static/web/css/navbar.css HTTP/1.1" 200 2303
INFO 2025-05-20 09:13:05,669 basehttp 32820 3192 "GET /static/web/css/footer.css HTTP/1.1" 200 383
INFO 2025-05-20 09:13:05,805 basehttp 32820 20576 "GET /static/web/img/Broadleaf.png HTTP/1.1" 200 18525
WARNING 2025-05-20 09:13:05,854 log 32820 20576 Not Found: /favicon.ico
WARNING 2025-05-20 09:13:05,854 basehttp 32820 20576 "GET /favicon.ico HTTP/1.1" 404 18562
ERROR 2025-05-20 09:13:53,080 log 32820 20576 Internal Server Error: /auth/signup/
Traceback (most recent call last):
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\venv\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py", line 60, in form_valid
    user = User.objects.create_user(**user_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: CustomUserManager.create_user() missing 1 required positional argument: 'name'
ERROR 2025-05-20 09:13:53,084 basehttp 32820 20576 "POST /auth/signup/ HTTP/1.1" 500 96517
INFO 2025-05-20 09:14:59,294 autoreload 32820 27304 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-05-20 09:15:00,643 autoreload 33268 29620 Watching for file changes with StatReloader
INFO 2025-05-20 09:17:50,771 basehttp 33268 23636 "POST /auth/signup/ HTTP/1.1" 302 0
INFO 2025-05-20 09:17:50,822 basehttp 33268 23636 "GET /auth/signin/ HTTP/1.1" 200 5825
INFO 2025-05-20 09:43:28,194 autoreload 33268 29620 C:\Local\Projects\avensus\Broadleaf\backend\web\admin_views.py changed, reloading.
INFO 2025-05-20 09:43:29,525 autoreload 22420 1548 Watching for file changes with StatReloader
INFO 2025-05-20 09:44:13,144 autoreload 22420 1548 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-20 09:44:14,680 autoreload 21784 34088 Watching for file changes with StatReloader
INFO 2025-05-20 09:48:24,178 autoreload 21784 34088 C:\Local\Projects\avensus\Broadleaf\backend\web\mixins.py changed, reloading.
INFO 2025-05-20 09:48:25,500 autoreload 34504 20420 Watching for file changes with StatReloader
INFO 2025-05-20 09:48:32,090 autoreload 34504 20420 C:\Local\Projects\avensus\Broadleaf\backend\web\inquiry_views.py changed, reloading.
INFO 2025-05-20 09:48:33,207 autoreload 3808 30316 Watching for file changes with StatReloader
INFO 2025-05-20 09:48:44,650 autoreload 3808 30316 C:\Local\Projects\avensus\Broadleaf\backend\web\mixins.py changed, reloading.
INFO 2025-05-20 09:48:45,944 autoreload 33828 27712 Watching for file changes with StatReloader
INFO 2025-05-20 10:00:23,359 autoreload 33828 27712 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-20 10:00:24,986 autoreload 29340 33752 Watching for file changes with StatReloader
INFO 2025-05-20 10:00:54,037 autoreload 29340 33752 C:\Local\Projects\avensus\Broadleaf\backend\properties\views.py changed, reloading.
INFO 2025-05-20 10:00:55,430 autoreload 6304 32724 Watching for file changes with StatReloader
INFO 2025-05-20 10:01:32,547 autoreload 6304 32724 C:\Local\Projects\avensus\Broadleaf\backend\properties\serializers.py changed, reloading.
INFO 2025-05-20 10:01:33,659 autoreload 31320 28692 Watching for file changes with StatReloader
INFO 2025-05-20 10:02:27,014 autoreload 31320 28692 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-20 10:02:28,558 autoreload 31292 34544 Watching for file changes with StatReloader
INFO 2025-05-20 10:03:08,360 autoreload 31292 34544 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-20 10:03:09,913 autoreload 32004 33108 Watching for file changes with StatReloader
INFO 2025-05-20 10:08:02,712 autoreload 32004 33108 C:\Local\Projects\avensus\Broadleaf\backend\web\property_views.py changed, reloading.
INFO 2025-05-20 10:08:04,218 autoreload 29448 29964 Watching for file changes with StatReloader
INFO 2025-05-20 10:26:21,990 autoreload 29448 29964 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-20 10:26:23,047 autoreload 23416 28052 Watching for file changes with StatReloader
INFO 2025-05-21 08:59:52,940 autoreload 23416 28052 C:\Local\Projects\avensus\Broadleaf\backend\web\admin_views.py changed, reloading.
INFO 2025-05-21 08:59:55,089 autoreload 35896 29256 Watching for file changes with StatReloader
INFO 2025-05-21 09:00:44,937 autoreload 35896 29256 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-21 09:00:46,391 autoreload 30876 23176 Watching for file changes with StatReloader
INFO 2025-05-21 09:04:35,433 autoreload 30876 23176 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-21 09:04:36,819 autoreload 32132 36644 Watching for file changes with StatReloader
INFO 2025-05-21 09:12:28,336 autoreload 32132 36644 C:\Local\Projects\avensus\Broadleaf\backend\properties\models.py changed, reloading.
INFO 2025-05-21 09:12:29,989 autoreload 2204 10260 Watching for file changes with StatReloader
INFO 2025-05-21 09:13:32,349 autoreload 2204 10260 C:\Local\Projects\avensus\Broadleaf\backend\web\urls.py changed, reloading.
INFO 2025-05-21 09:13:33,708 autoreload 34004 34332 Watching for file changes with StatReloader
INFO 2025-05-21 09:14:08,350 autoreload 34004 34332 C:\Local\Projects\avensus\Broadleaf\backend\web\inquiry_views.py changed, reloading.
INFO 2025-05-21 09:14:09,484 autoreload 32404 13300 Watching for file changes with StatReloader
INFO 2025-05-21 13:28:42,212 autoreload 24636 33384 Watching for file changes with StatReloader
INFO 2025-05-21 13:43:19,317 autoreload 30876 8176 Watching for file changes with StatReloader
INFO 2025-05-21 14:29:16,643 autoreload 30876 8176 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-21 14:29:18,253 autoreload 31172 17284 Watching for file changes with StatReloader
INFO 2025-05-23 07:06:17,107 autoreload 36316 35716 Watching for file changes with StatReloader
INFO 2025-05-23 07:08:53,021 basehttp 36316 37028 "GET / HTTP/1.1" 200 15224
INFO 2025-05-23 07:08:53,196 basehttp 36316 37028 "GET /static/web/css/navbar.css HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,201 basehttp 36316 34988 "GET /static/web/css/footer.css HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,210 basehttp 36316 37028 "GET /static/web/img/Search.svg HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,210 basehttp 36316 34988 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,221 basehttp 36316 37028 "GET /static/web/img/Cover.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,221 basehttp 36316 34988 "GET /static/web/img/Profile.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,229 basehttp 36316 37028 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,229 basehttp 36316 34988 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,236 basehttp 36316 37028 "GET /static/web/img/Google.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,239 basehttp 36316 34988 "GET /static/web/img/Apple.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,241 basehttp 36316 37028 "GET /static/web/img/app%20download.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,457 basehttp 36316 35088 "GET /static/web/css/home.css HTTP/1.1" 200 7775
INFO 2025-05-23 07:08:53,477 basehttp 36316 35088 "GET /static/web/img/landing-image.png HTTP/1.1" 304 0
INFO 2025-05-23 07:08:53,479 basehttp 36316 37028 "GET /static/web/img/about.png HTTP/1.1" 304 0
INFO 2025-05-23 07:09:00,784 basehttp 36316 37028 "GET /auth/signin/ HTTP/1.1" 200 5874
INFO 2025-05-23 07:09:00,849 basehttp 36316 37028 "GET /static/web/css/signin.css HTTP/1.1" 304 0
INFO 2025-05-23 07:26:32,932 basehttp 36316 18100 "GET /auth/signup/ HTTP/1.1" 200 6204
INFO 2025-05-23 07:26:33,029 basehttp 36316 18100 "GET /static/web/css/signup.css HTTP/1.1" 304 0
INFO 2025-05-23 07:27:44,115 basehttp 36316 18100 "POST /auth/signup/ HTTP/1.1" 200 6631
INFO 2025-05-23 07:28:19,194 basehttp 36316 18100 "POST /auth/signup/ HTTP/1.1" 200 6456
INFO 2025-05-23 08:02:16,841 autoreload 36316 35716 C:\Local\Projects\avensus\Broadleaf\backend\web\inquiry_views.py changed, reloading.
INFO 2025-05-23 08:02:18,392 autoreload 29372 28548 Watching for file changes with StatReloader
INFO 2025-05-28 10:48:51,569 autoreload 32736 37112 Watching for file changes with StatReloader
INFO 2025-05-28 11:25:15,337 basehttp 32736 48392 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-05-28 11:25:16,080 basehttp 32736 48392 "GET /api/schema/ HTTP/1.1" 200 143194
INFO 2025-05-28 11:30:19,528 autoreload 32736 37112 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 11:30:21,174 autoreload 49052 46612 Watching for file changes with StatReloader
INFO 2025-05-28 11:34:12,090 autoreload 49052 46612 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 11:34:13,320 autoreload 48692 29232 Watching for file changes with StatReloader
INFO 2025-05-28 11:34:46,712 autoreload 48692 29232 C:\Local\Projects\avensus\Broadleaf\backend\web\api_urls.py changed, reloading.
INFO 2025-05-28 11:34:47,783 autoreload 46428 42396 Watching for file changes with StatReloader
INFO 2025-05-28 11:35:48,484 autoreload 23548 46332 Watching for file changes with StatReloader
INFO 2025-05-28 11:36:50,815 basehttp 23548 48696 "GET /api/docs/ HTTP/1.1" 200 4708
INFO 2025-05-28 11:36:51,252 basehttp 23548 48696 "GET /api/schema/ HTTP/1.1" 200 148648
WARNING 2025-05-28 11:37:19,898 log 23548 48328 Unauthorized: /api/auth/mobile/signup/
WARNING 2025-05-28 11:37:19,902 basehttp 23548 48328 "POST /api/auth/mobile/signup/ HTTP/1.1" 401 58
INFO 2025-05-28 15:28:36,081 basehttp 23548 49252 "POST /api/token/ HTTP/1.1" 200 483
WARNING 2025-05-28 15:29:07,147 log 23548 49252 Bad Request: /api/users/users/
WARNING 2025-05-28 15:29:07,147 basehttp 23548 49252 "POST /api/users/users/ HTTP/1.1" 400 50
INFO 2025-05-28 15:29:23,342 basehttp 23548 49252 "GET /api/properties/properties/ HTTP/1.1" 200 2
WARNING 2025-05-28 15:30:04,955 log 23548 49252 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:30:04,955 basehttp 23548 49252 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 181
WARNING 2025-05-28 15:30:32,226 log 23548 49252 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:30:32,228 basehttp 23548 49252 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 177
WARNING 2025-05-28 15:30:45,383 log 23548 49252 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:30:45,387 basehttp 23548 49252 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 142
WARNING 2025-05-28 15:31:02,483 log 23548 49252 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:31:02,483 basehttp 23548 49252 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 107
ERROR 2025-05-28 15:31:11,723 log 23548 49252 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:31:11,723 basehttp 23548 49252 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 106
INFO 2025-05-28 15:38:13,703 autoreload 23548 46332 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-28 15:38:16,087 autoreload 42568 25344 Watching for file changes with StatReloader
WARNING 2025-05-28 15:39:54,881 log 42568 27524 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:39:54,881 basehttp 42568 27524 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 161
ERROR 2025-05-28 15:40:08,465 log 42568 27524 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:40:08,465 basehttp 42568 27524 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 107
INFO 2025-05-28 15:44:54,222 autoreload 42568 25344 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 15:44:55,803 autoreload 51136 11484 Watching for file changes with StatReloader
INFO 2025-05-28 15:45:22,273 autoreload 51136 11484 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:45:23,843 autoreload 8340 48380 Watching for file changes with StatReloader
WARNING 2025-05-28 15:46:01,091 log 8340 19268 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:46:01,099 basehttp 8340 19268 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 161
ERROR 2025-05-28 15:46:10,703 log 8340 19268 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:46:10,711 basehttp 8340 19268 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 107
INFO 2025-05-28 15:46:28,296 autoreload 49220 49100 Watching for file changes with StatReloader
ERROR 2025-05-28 15:46:40,774 log 49220 11416 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:46:40,782 basehttp 49220 11416 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 107
INFO 2025-05-28 15:49:01,079 autoreload 49220 49100 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:49:02,618 autoreload 50996 50840 Watching for file changes with StatReloader
WARNING 2025-05-28 15:49:07,932 log 50996 21972 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:49:07,934 basehttp 50996 21972 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 161
ERROR 2025-05-28 15:49:16,832 log 50996 21972 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:49:16,832 basehttp 50996 21972 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 107
INFO 2025-05-28 15:49:35,311 autoreload 50996 50840 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:49:37,303 autoreload 8092 8980 Watching for file changes with StatReloader
WARNING 2025-05-28 15:49:51,838 log 8092 48132 Bad Request: /api/auth/mobile/signup/
WARNING 2025-05-28 15:49:51,838 basehttp 8092 48132 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 161
ERROR 2025-05-28 15:50:02,429 log 8092 48132 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:50:02,432 basehttp 8092 48132 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 2531
INFO 2025-05-28 15:51:29,128 autoreload 8092 8980 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:51:30,854 autoreload 41792 49840 Watching for file changes with StatReloader
ERROR 2025-05-28 15:51:42,311 log 41792 46784 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:51:42,312 basehttp 41792 46784 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 1911
INFO 2025-05-28 15:52:06,016 autoreload 41792 49840 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:52:07,586 autoreload 33856 48104 Watching for file changes with StatReloader
ERROR 2025-05-28 15:52:18,472 log 33856 43936 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:52:18,476 basehttp 33856 43936 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 2610
INFO 2025-05-28 15:53:51,434 autoreload 33856 48104 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:53:53,165 autoreload 40968 43928 Watching for file changes with StatReloader
INFO 2025-05-28 15:53:57,098 autoreload 40968 43928 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 15:53:58,506 autoreload 46880 47420 Watching for file changes with StatReloader
ERROR 2025-05-28 15:54:10,997 log 46880 37976 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 15:54:11,002 basehttp 46880 37976 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 347
INFO 2025-05-28 15:55:06,455 autoreload 46880 47420 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 15:55:08,181 autoreload 44248 40760 Watching for file changes with StatReloader
INFO 2025-05-28 15:55:17,891 basehttp 44248 38728 "POST /api/auth/mobile/signup/ HTTP/1.1" 201 105
INFO 2025-05-28 17:21:19,404 autoreload 44248 40760 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 17:21:22,194 autoreload 37228 34080 Watching for file changes with StatReloader
INFO 2025-05-28 17:21:32,668 autoreload 37228 34080 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 17:21:35,239 autoreload 35248 51404 Watching for file changes with StatReloader
INFO 2025-05-28 17:21:46,739 autoreload 35248 51404 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 17:21:48,842 autoreload 19324 40392 Watching for file changes with StatReloader
INFO 2025-05-28 17:23:17,140 autoreload 19324 40392 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 17:23:18,949 autoreload 49600 47952 Watching for file changes with StatReloader
INFO 2025-05-28 17:24:13,310 autoreload 49600 47952 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 17:24:15,113 autoreload 19692 43388 Watching for file changes with StatReloader
INFO 2025-05-28 17:26:03,547 autoreload 44940 42404 Watching for file changes with StatReloader
WARNING 2025-05-28 17:26:15,210 log 44940 29188 Unauthorized: /api/auth/mobile/signup/
WARNING 2025-05-28 17:26:15,211 basehttp 44940 29188 "POST /api/auth/mobile/signup/ HTTP/1.1" 401 172
INFO 2025-05-28 17:26:27,718 basehttp 44940 29188 "POST /api/token/ HTTP/1.1" 200 483
ERROR 2025-05-28 17:27:11,287 log 44940 29188 Internal Server Error: /api/auth/mobile/signup/
ERROR 2025-05-28 17:27:11,289 basehttp 44940 29188 "POST /api/auth/mobile/signup/ HTTP/1.1" 500 347
INFO 2025-05-28 17:28:10,347 autoreload 44940 42404 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 17:28:12,222 autoreload 46088 19324 Watching for file changes with StatReloader
INFO 2025-05-28 17:28:33,250 basehttp 46088 46572 "POST /api/auth/mobile/signup/ HTTP/1.1" 201 105
INFO 2025-05-28 17:33:10,557 autoreload 46088 19324 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 17:33:12,264 autoreload 32288 46616 Watching for file changes with StatReloader
INFO 2025-05-28 17:33:32,263 autoreload 32288 46616 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 17:33:33,603 autoreload 50660 10064 Watching for file changes with StatReloader
INFO 2025-05-28 17:33:49,857 autoreload 50660 10064 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 17:33:51,153 autoreload 35512 46392 Watching for file changes with StatReloader
INFO 2025-05-28 17:34:01,694 autoreload 35512 46392 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 17:34:03,457 autoreload 31572 46424 Watching for file changes with StatReloader
INFO 2025-05-28 17:37:39,553 autoreload 31572 46424 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-05-28 17:37:40,990 autoreload 51244 27992 Watching for file changes with StatReloader
INFO 2025-05-28 17:37:47,869 autoreload 51244 27992 C:\Local\Projects\avensus\Broadleaf\backend\core\services\email_service.py changed, reloading.
INFO 2025-05-28 17:37:49,112 autoreload 28096 6124 Watching for file changes with StatReloader
INFO 2025-05-28 17:38:21,349 basehttp 28096 36964 "POST /api/auth/mobile/signup/ HTTP/1.1" 201 105
INFO 2025-05-29 17:46:06,176 autoreload 28096 6124 C:\Local\Projects\avensus\Broadleaf\backend\users\serializers.py changed, reloading.
INFO 2025-05-29 17:46:08,246 autoreload 38964 22936 Watching for file changes with StatReloader
INFO 2025-05-29 17:46:46,581 autoreload 38964 22936 C:\Local\Projects\avensus\Broadleaf\backend\users\serializers.py changed, reloading.
INFO 2025-05-29 17:46:47,824 autoreload 42556 25688 Watching for file changes with StatReloader
INFO 2025-05-29 17:49:14,831 autoreload 42556 25688 C:\Local\Projects\avensus\Broadleaf\backend\users\serializers.py changed, reloading.
INFO 2025-05-29 17:49:16,832 autoreload 47292 51268 Watching for file changes with StatReloader
INFO 2025-05-29 17:49:23,940 autoreload 47292 51268 C:\Local\Projects\avensus\Broadleaf\backend\backend\settings.py changed, reloading.
INFO 2025-05-29 17:49:27,011 autoreload 50044 36516 Watching for file changes with StatReloader
INFO 2025-05-29 18:10:51,137 autoreload 50044 36516 C:\Local\Projects\avensus\Broadleaf\backend\users\serializers.py changed, reloading.
INFO 2025-05-29 18:10:52,391 autoreload 29376 41232 Watching for file changes with StatReloader
INFO 2025-05-29 18:11:09,822 basehttp 29376 49712 "POST /api/token/ HTTP/1.1" 200 673
INFO 2025-05-29 18:12:51,484 basehttp 29376 41032 "GET /api/properties/properties/ HTTP/1.1" 200 2
INFO 2025-06-01 16:17:08,052 autoreload 23920 9388 Watching for file changes with StatReloader
INFO 2025-06-02 15:29:03,542 autoreload 3112 32856 Watching for file changes with StatReloader
INFO 2025-06-02 15:29:22,488 basehttp 3112 35328 "POST /api/token/ HTTP/1.1" 200 673
WARNING 2025-06-02 15:31:28,044 log 3112 3756 Bad Request: /api/auth/mobile/signup/
WARNING 2025-06-02 15:31:28,046 basehttp 3112 3756 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 136
INFO 2025-06-02 15:32:01,402 basehttp 3112 3756 "POST /api/auth/mobile/signup/ HTTP/1.1" 201 71
WARNING 2025-06-02 15:32:28,612 log 3112 3756 Not Found: /api/auth/mobile/verify/
WARNING 2025-06-02 15:32:28,613 basehttp 3112 3756 "POST /api/auth/mobile/verify/ HTTP/1.1" 404 64
INFO 2025-06-02 15:37:10,061 autoreload 3112 32856 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-06-02 15:37:11,301 autoreload 29092 31152 Watching for file changes with StatReloader
INFO 2025-06-02 15:37:18,855 autoreload 29092 31152 C:\Local\Projects\avensus\Broadleaf\backend\web\api_views.py changed, reloading.
INFO 2025-06-02 15:37:19,990 autoreload 33020 21816 Watching for file changes with StatReloader
WARNING 2025-06-02 15:38:27,798 log 33020 4648 Bad Request: /api/auth/mobile/signup/
WARNING 2025-06-02 15:38:27,801 basehttp 33020 4648 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 110
WARNING 2025-06-02 15:38:44,331 log 33020 4648 Bad Request: /api/auth/mobile/signup/
WARNING 2025-06-02 15:38:44,332 basehttp 33020 4648 "POST /api/auth/mobile/signup/ HTTP/1.1" 400 110
INFO 2025-06-02 15:38:55,979 basehttp 33020 4648 "POST /api/auth/mobile/signup/ HTTP/1.1" 201 122
INFO 2025-06-02 15:39:17,704 basehttp 33020 4648 "POST /api/auth/mobile/verify/ HTTP/1.1" 200 168
INFO 2025-06-03 11:08:41,901 autoreload 14772 36424 Watching for file changes with StatReloader
INFO 2025-06-03 11:08:52,191 basehttp 14772 34400 "GET / HTTP/1.1" 200 15392
INFO 2025-06-03 11:08:53,348 basehttp 14772 34800 "GET /static/web/css/navbar.css HTTP/1.1" 200 2299
INFO 2025-06-03 11:08:53,352 basehttp 14772 34400 "GET /static/web/css/footer.css HTTP/1.1" 200 383
INFO 2025-06-03 11:08:53,368 basehttp 14772 34800 "GET /static/web/css/home.css HTTP/1.1" 200 7775
INFO 2025-06-03 11:08:53,374 basehttp 14772 34400 "GET /static/web/img/Search.svg HTTP/1.1" 200 483
INFO 2025-06-03 11:08:53,376 basehttp 14772 34800 "GET /static/web/img/Broadleaf.png HTTP/1.1" 304 0
INFO 2025-06-03 11:09:01,753 basehttp 14772 34800 "GET /static/web/img/landing-image.png HTTP/1.1" 200 2091106
INFO 2025-06-03 11:09:22,523 basehttp 14772 34800 "GET /static/web/img/Cover.png HTTP/1.1" 200 225390
INFO 2025-06-03 11:09:22,532 basehttp 14772 34800 "GET /static/web/img/Profile.png HTTP/1.1" 200 8670
INFO 2025-06-03 11:09:22,548 basehttp 14772 34800 "GET /static/web/img/Profile%203%20(1).png HTTP/1.1" 200 5608
INFO 2025-06-03 11:09:22,565 basehttp 14772 34800 "GET /static/web/img/Profile%203%20(2).png HTTP/1.1" 200 7044
INFO 2025-06-03 11:09:22,573 basehttp 14772 34800 "GET /static/web/img/Google.png HTTP/1.1" 200 8451
INFO 2025-06-03 11:09:22,588 basehttp 14772 34800 "GET /static/web/img/Apple.png HTTP/1.1" 200 8490
INFO 2025-06-03 11:09:22,614 basehttp 14772 34800 "GET /static/web/img/app%20download.png HTTP/1.1" 200 296858
INFO 2025-06-03 11:09:22,706 basehttp 14772 34800 "GET /static/web/img/about.png HTTP/1.1" 200 1681290
INFO 2025-06-03 11:09:31,091 basehttp 14772 34800 "GET /auth/signin/ HTTP/1.1" 200 6048
INFO 2025-06-03 11:09:31,153 basehttp 14772 34800 "GET /static/web/css/signin.css HTTP/1.1" 200 674
INFO 2025-06-03 11:09:33,963 basehttp 14772 34800 "GET /auth/signup/ HTTP/1.1" 200 6377
INFO 2025-06-03 11:09:34,021 basehttp 14772 34800 "GET /static/web/css/signup.css HTTP/1.1" 200 673
INFO 2025-06-03 11:10:14,919 basehttp 14772 34800 "POST /auth/signup/ HTTP/1.1" 200 6616
WARNING 2025-06-03 11:10:16,528 log 14772 34800 Not Found: /favicon.ico
WARNING 2025-06-03 11:10:16,530 basehttp 14772 34800 "GET /favicon.ico HTTP/1.1" 404 22902
INFO 2025-06-03 11:10:41,375 basehttp 14772 34800 "POST /auth/signup/ HTTP/1.1" 200 6616
INFO 2025-06-03 11:11:11,382 basehttp 14772 34800 "POST /auth/signup/ HTTP/1.1" 302 0
INFO 2025-06-03 11:11:11,407 basehttp 14772 34800 "GET /auth/signin/ HTTP/1.1" 200 6640
INFO 2025-06-03 11:13:25,616 basehttp 14772 34800 "POST /auth/signin/ HTTP/1.1" 200 6061
INFO 2025-06-03 11:17:08,437 autoreload 14772 36424 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-06-03 11:17:09,771 autoreload 23936 36460 Watching for file changes with StatReloader
INFO 2025-06-03 11:17:21,090 autoreload 23936 36460 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-06-03 11:17:22,462 autoreload 1968 21556 Watching for file changes with StatReloader
INFO 2025-06-03 11:17:33,907 autoreload 1968 21556 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-06-03 11:17:35,395 autoreload 35608 22924 Watching for file changes with StatReloader
INFO 2025-06-03 11:17:48,413 basehttp 35608 11928 "POST /auth/signin/ HTTP/1.1" 200 6061
INFO 2025-06-03 11:19:48,478 autoreload 35608 22924 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:19:49,771 autoreload 36280 23696 Watching for file changes with StatReloader
INFO 2025-06-03 11:20:03,361 autoreload 36280 23696 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:20:04,507 autoreload 34904 30992 Watching for file changes with StatReloader
INFO 2025-06-03 11:21:47,882 basehttp 34904 32780 "POST /auth/signin/ HTTP/1.1" 200 6061
INFO 2025-06-03 11:22:14,401 basehttp 34904 32780 "POST /auth/signin/ HTTP/1.1" 200 6061
INFO 2025-06-03 11:24:42,383 autoreload 34904 30992 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:24:43,626 autoreload 17604 11364 Watching for file changes with StatReloader
INFO 2025-06-03 11:25:13,294 basehttp 17604 36096 "POST /auth/signin/ HTTP/1.1" 200 6577
INFO 2025-06-03 11:25:29,051 basehttp 17604 36096 "POST /auth/signin/ HTTP/1.1" 200 6577
INFO 2025-06-03 11:25:45,269 basehttp 17604 36096 "GET /auth/signin/ HTTP/1.1" 200 6048
INFO 2025-06-03 11:26:00,416 basehttp 17604 36096 "POST /auth/signin/ HTTP/1.1" 200 6577
INFO 2025-06-03 11:27:03,127 autoreload 17604 11364 C:\Local\Projects\avensus\Broadleaf\backend\web\forms.py changed, reloading.
INFO 2025-06-03 11:27:04,314 autoreload 33124 31540 Watching for file changes with StatReloader
INFO 2025-06-03 11:27:22,831 autoreload 33124 31540 C:\Local\Projects\avensus\Broadleaf\backend\web\forms.py changed, reloading.
INFO 2025-06-03 11:27:24,132 autoreload 26640 31704 Watching for file changes with StatReloader
INFO 2025-06-03 11:27:59,529 basehttp 26640 35348 "POST /auth/signin/ HTTP/1.1" 200 6586
INFO 2025-06-03 11:28:15,704 basehttp 26640 35348 "POST /auth/signin/ HTTP/1.1" 200 6586
INFO 2025-06-03 11:28:18,572 basehttp 26640 35348 "GET /auth/signup/ HTTP/1.1" 200 6377
INFO 2025-06-03 11:28:48,405 basehttp 26640 35348 "POST /auth/signup/ HTTP/1.1" 302 0
INFO 2025-06-03 11:28:48,414 basehttp 26640 35348 "GET /auth/signin/ HTTP/1.1" 200 6638
INFO 2025-06-03 11:29:17,552 basehttp 26640 35348 "POST /auth/signin/ HTTP/1.1" 200 6589
INFO 2025-06-03 11:29:30,857 basehttp 26640 35348 "POST /auth/signin/ HTTP/1.1" 200 6743
INFO 2025-06-03 11:29:57,370 basehttp 26640 35348 "POST /auth/signin/ HTTP/1.1" 200 6589
INFO 2025-06-03 11:37:07,521 autoreload 26640 31704 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:37:08,798 autoreload 24240 9236 Watching for file changes with StatReloader
INFO 2025-06-03 11:42:23,803 autoreload 24240 9236 C:\Local\Projects\avensus\Broadleaf\backend\web\views.py changed, reloading.
INFO 2025-06-03 11:42:25,733 autoreload 20804 33380 Watching for file changes with StatReloader
INFO 2025-06-03 11:42:33,121 autoreload 20804 33380 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:42:34,188 autoreload 24336 35436 Watching for file changes with StatReloader
INFO 2025-06-03 11:43:44,159 autoreload 24336 35436 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:43:45,307 autoreload 35808 36584 Watching for file changes with StatReloader
INFO 2025-06-03 11:44:35,523 autoreload 35808 36584 C:\Local\Projects\avensus\Broadleaf\backend\web\auth_views.py changed, reloading.
INFO 2025-06-03 11:44:37,061 autoreload 5252 26752 Watching for file changes with StatReloader
INFO 2025-06-04 12:22:35,221 autoreload 25324 4868 Watching for file changes with StatReloader
INFO 2025-06-06 12:42:06,648 autoreload 30592 28852 Watching for file changes with StatReloader
