# Generated by Django 3.2.19 on 2023-06-30 13:16

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("socialaccount", "0003_extra_data_default_dict"),
    ]

    operations = [
        migrations.AddField(
            model_name="socialapp",
            name="provider_id",
            field=models.CharField(
                blank=True, max_length=200, verbose_name="provider ID"
            ),
        ),
        migrations.AddField(
            model_name="socialapp",
            name="settings",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="socialaccount",
            name="provider",
            field=models.Char<PERSON>ield(max_length=200, verbose_name="provider"),
        ),
    ]
