from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from .views import UserViewSet, RoleViewSet, AgentViewSet, health_check

router = DefaultRouter()


router.register(r'users', UserViewSet, basename='user')
router.register(r'roles', RoleViewSet, basename='role')
router.register(r'agents', AgentViewSet, basename='agent')  

urlpatterns = [
    path('', include(router.urls)),
    path('health/', health_check, name='health_check'),
]
