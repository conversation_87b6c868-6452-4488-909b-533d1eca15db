# Generated by Django 5.1.7 on 2025-05-01 11:41

import django.db.models.deletion
import django.utils.timezone
import properties.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0002_transaction'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='image',
            options={'ordering': ['-is_primary', '-uploaded_at']},
        ),
        migrations.RemoveField(
            model_name='image',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='image',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='image',
            name='uploaded_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='image',
            name='caption',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='image',
            name='image',
            field=models.ImageField(upload_to=properties.models.property_image_path),
        ),
        migrations.AlterField(
            model_name='property',
            name='agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='properties', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='property',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AlterField(
            model_name='property',
            name='size',
            field=models.DecimalField(decimal_places=2, help_text='Size in square feet', max_digits=10),
        ),
        migrations.AlterField(
            model_name='property',
            name='type',
            field=models.CharField(choices=[('HOUSE', 'House'), ('APARTMENT', 'Apartment'), ('CONDO', 'Condominium'), ('TOWNHOUSE', 'Townhouse'), ('LAND', 'Land'), ('COMMERCIAL', 'Commercial'), ('OTHER', 'Other')], default='HOUSE', max_length=20),
        ),
        migrations.AlterModelTable(
            name='image',
            table=None,
        ),
        migrations.AlterModelTable(
            name='property',
            table=None,
        ),
    ]
