import base64
import os
from django.core.management.base import BaseCommand
from properties.models import Image


class Command(BaseCommand):
    help = "Converts images to base64 and stores them in the database"

    def add_arguments(self, parser):
        parser.add_argument(
            "--limit",
            type=int,
            help="Limit the number of images to process",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force conversion even if image_data already exists",
        )

    def handle(self, *args, **options):
        limit = options.get('limit')
        force = options.get('force', False)
        
        # Get images that don't have base64 data yet (or all if force=True)
        query = Image.objects.all()
        if not force:
            query = query.filter(image_data='')
            
        if limit:
            query = query[:limit]
            
        total = query.count()
        self.stdout.write(f"Converting {total} images to base64...")
        
        success_count = 0
        error_count = 0
        
        for img in query:
            try:
                # Check if the image file exists
                if not os.path.exists(img.image.path):
                    self.stdout.write(self.style.WARNING(
                        f"Image file not found: {img.image.path}"
                    ))
                    error_count += 1
                    continue
                
                # Read the image file and convert to base64
                with open(img.image.path, 'rb') as f:
                    image_data = base64.b64encode(f.read()).decode('utf-8')
                
                # Store the base64 data
                img.image_data = image_data
                img.save(update_fields=['image_data'])
                
                success_count += 1
                if success_count % 10 == 0:
                    self.stdout.write(f"Processed {success_count}/{total} images...")
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error processing image {img.image_id}: {str(e)}"
                ))
                error_count += 1
        
        self.stdout.write(self.style.SUCCESS(
            f"Conversion complete. Success: {success_count}, Errors: {error_count}"
        ))