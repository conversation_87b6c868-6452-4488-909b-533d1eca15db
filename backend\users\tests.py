from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from .models import Role
import time

User = get_user_model()

class RoleModelTest(TestCase):
    def setUp(self):
        self.role_data = {
            'role_name': 'CLIENT',
            'description': 'Test client role'
        }
        self.role = Role.objects.create(**self.role_data)

    def test_role_creation(self):
        """Test that a role can be created with valid data"""
        self.assertEqual(self.role.role_name, self.role_data['role_name'])
        self.assertEqual(self.role.description, self.role_data['description'])
        self.assertTrue(self.role.role_id > 0)

    def test_role_str(self):
        """Test the string representation of the role"""
        self.assertEqual(str(self.role), self.role_data['role_name'])

    def test_role_choices(self):
        """Test that role_name only accepts valid choices"""
        # Delete the existing role to avoid unique constraint violation
        self.role.delete()
        
        valid_choices = ['CLIENT', 'AGENT', 'ADMIN']
        for choice in valid_choices:
            role = Role.objects.create(role_name=choice)
            self.assertIn(role.role_name, valid_choices)
            role.full_clean()  # This will validate against choices

        # Test invalid choice
        invalid_role = Role(role_name='INVALID')
        with self.assertRaises(ValidationError):
            invalid_role.full_clean()

    def test_role_unique_constraint(self):
        """Test that role_name must be unique"""
        with self.assertRaises(Exception):
            Role.objects.create(role_name=self.role_data['role_name'])

class UserModelTest(TestCase):
    def setUp(self):
        self.role = Role.objects.create(role_name='CLIENT')
        self.user_data = {
            'username': '<EMAIL>',  # Required by Django's User model
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'role': self.role,
            'phone_number': '1234567890'
        }
        self.user = User.objects.create_user(**self.user_data)

    def test_user_creation(self):
        """Test that a user can be created with valid data"""
        self.assertEqual(self.user.name, self.user_data['name'])
        self.assertEqual(self.user.email, self.user_data['email'])
        self.assertEqual(self.user.role, self.role)
        self.assertEqual(self.user.phone_number, self.user_data['phone_number'])
        self.assertTrue(self.user.user_id > 0)
        self.assertTrue(self.user.check_password(self.user_data['password']))

    def test_user_str(self):
        """Test the string representation of the user"""
        expected_str = f"{self.user_data['name']} ({self.user_data['email']})"
        self.assertEqual(str(self.user), expected_str)

    def test_user_email_unique(self):
        """Test that email must be unique"""
        with self.assertRaises(Exception):
            User.objects.create_user(
                username='<EMAIL>',
                name='Another User',
                email=self.user_data['email'],
                password='testpass123',
                role=self.role
            )

    def test_user_required_fields(self):
        """Test that required fields are enforced"""
        with self.assertRaises(Exception):
            User.objects.create_user(
                username='<EMAIL>',
                email='<EMAIL>',
                password='testpass123'
            )

    def test_user_created_at(self):
        """Test that created_at is set correctly"""
        self.assertIsNotNone(self.user.created_at)
        self.assertLessEqual(self.user.created_at, timezone.now())

    def test_user_updated_at(self):
        """Test that updated_at is updated on changes"""
        original_updated_at = self.user.updated_at
        time.sleep(0.1)  # Add a small delay to ensure timestamp difference
        self.user.name = 'Updated Name'
        self.user.save()
        self.user.refresh_from_db()  # Reload the instance from the database
        self.assertGreater(self.user.updated_at, original_updated_at)

    def test_user_phone_number_optional(self):
        """Test that phone_number is optional"""
        user = User.objects.create_user(
            username='<EMAIL>',
            name='No Phone User',
            email='<EMAIL>',
            password='testpass123',
            role=self.role
        )
        self.assertEqual(user.phone_number, '')

    def test_user_role_protection(self):
        """Test that user's role cannot be deleted if it has users"""
        with self.assertRaises(Exception):
            self.role.delete()
