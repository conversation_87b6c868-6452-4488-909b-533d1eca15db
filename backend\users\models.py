from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.utils import timezone

class Role(models.Model):
    ROLE_CHOICES = [
        ('CLIENT', 'Client'),
        ('AGENT', 'Agent'),
        ('ADMIN', 'Admin'),
    ]

    role_id = models.AutoField(primary_key=True)
    role_name = models.CharField(max_length=10, choices=ROLE_CHOICES, unique=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.role_name

    class Meta:
        db_table = 'roles'

class CustomUserManager(BaseUserManager):
    def create_user(self, email, name, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, name=name, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, name, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        
        # Handle role if it's passed as an ID instead of a Role instance
        if 'role' in extra_fields and not isinstance(extra_fields['role'], Role):
            try:
                extra_fields['role'] = Role.objects.get(role_id=extra_fields['role'])
            except Role.DoesNotExist:
                # Default to ADMIN role if not found
                extra_fields['role'], _ = Role.objects.get_or_create(role_name='ADMIN')
        
        # If no role is provided, use ADMIN role
        if 'role' not in extra_fields:
            extra_fields['role'], _ = Role.objects.get_or_create(role_name='ADMIN')
            
        return self.create_user(email, name, password, **extra_fields)

class User(AbstractUser):
    ROLE_CHOICES = [
        ('CLIENT', 'Client'),
        ('AGENT', 'Agent'),
        ('ADMIN', 'Admin'),
    ]
    
    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('PENDING', 'Pending'),
        ('SUSPENDED', 'Suspended'),
        ('INACTIVE', 'Inactive'),
    ]

    user_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    role = models.ForeignKey(Role, on_delete=models.PROTECT, related_name='users')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='ACTIVE')
    phone_number = models.CharField(max_length=15, blank=True)
    agent_description = models.TextField(blank=True, help_text="Agent's biography or description")
    profile_image = models.ImageField(upload_to='profile_images/', blank=True, null=True, help_text="Agent's profile image")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    objects = CustomUserManager()  # Set the custom manager

    # Override the default username field to use email instead
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['name', 'role']

    def __str__(self):
        return f"{self.name} ({self.email})"

    class Meta:
        db_table = 'users'

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = self.email
        super().save(*args, **kwargs)

    @property
    def is_agent(self):
        """Check if user is an agent"""
        return self.role.role_name == 'AGENT'

class VerificationCode(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verification_codes')
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'code']),
            models.Index(fields=['expires_at']),
        ]
        
    def is_valid(self):
        return timezone.now() < self.expires_at
