# Mobile Property Management API Integration Guide

This guide provides comprehensive integration notes, examples, and best practices for mobile app developers implementing property management functionality using the Broadleaf API.

## Quick Start

### 1. Authentication Setup

First, obtain a JWT token by authenticating with the API:

```javascript
// Example using fetch API
const login = async (email, password) => {
  const response = await fetch('http://localhost:8000/api/token/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password }),
  });
  
  const data = await response.json();
  if (response.ok) {
    // Store tokens securely
    localStorage.setItem('access_token', data.access);
    localStorage.setItem('refresh_token', data.refresh);
    return data;
  }
  throw new Error(data.detail || 'Login failed');
};
```

### 2. API Client Setup

Create a reusable API client with authentication:

```javascript
class PropertyAPI {
  constructor(baseURL = 'http://localhost:8000/api/properties') {
    this.baseURL = baseURL;
  }

  async request(endpoint, options = {}) {
    const token = localStorage.getItem('access_token');
    const url = `${this.baseURL}${endpoint}`;
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);
    
    if (response.status === 401) {
      // Token expired, try to refresh
      await this.refreshToken();
      return this.request(endpoint, options);
    }
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'API request failed');
    }
    
    return response.json();
  }

  async refreshToken() {
    const refreshToken = localStorage.getItem('refresh_token');
    const response = await fetch('http://localhost:8000/api/token/refresh/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh: refreshToken }),
    });
    
    if (response.ok) {
      const data = await response.json();
      localStorage.setItem('access_token', data.access);
    } else {
      // Refresh failed, redirect to login
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      throw new Error('Session expired');
    }
  }
}

const api = new PropertyAPI();
```

## Core Integration Examples

### 1. Property Listing with Search and Filters

```javascript
// Get properties with filters
const getProperties = async (filters = {}) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      params.append(key, value);
    }
  });
  
  const endpoint = `/properties/?${params.toString()}`;
  return api.request(endpoint);
};

// Usage example
const searchProperties = async () => {
  try {
    const properties = await getProperties({
      search: 'apartment',
      min_price: 100000,
      max_price: 500000,
      type: 'APARTMENT',
      bedrooms: 2,
      listing_type: 'SALE',
      page: 1,
      page_size: 10
    });
    
    console.log('Found properties:', properties.results);
    return properties;
  } catch (error) {
    console.error('Search failed:', error.message);
  }
};
```

### 2. Property Creation (Mobile Optimized)

```javascript
// Get amenities and attractions for property creation
const getPropertyOptions = async () => {
  try {
    const [amenities, attractions] = await Promise.all([
      api.request('/amenities/'),
      api.request('/attractions/')
    ]);
    
    return { amenities, attractions };
  } catch (error) {
    console.error('Failed to load options:', error.message);
    return { amenities: [], attractions: [] };
  }
};

// Create a new property
const createProperty = async (propertyData) => {
  try {
    const property = await api.request('/properties/mobile-create/', {
      method: 'POST',
      body: JSON.stringify(propertyData),
    });
    
    console.log('Property created:', property);
    return property;
  } catch (error) {
    console.error('Property creation failed:', error.message);
    throw error;
  }
};

// Usage example
const createNewProperty = async () => {
  const propertyData = {
    title: 'Beautiful Family Home',
    description: 'Spacious 3-bedroom house with garden',
    price: 350000.00,
    location: 'Suburban Area, City',
    size: 150.75,
    type: 'HOUSE',
    bedrooms: 3,
    listing_type: 'SALE',
    amenity_ids: [1, 2, 5], // Swimming Pool, Gym, Parking
    attraction_ids: [1, 3], // Shopping Mall, School
    is_featured: false
  };
  
  try {
    const newProperty = await createProperty(propertyData);
    // Navigate to property details or show success message
    return newProperty;
  } catch (error) {
    // Handle validation errors
    if (error.message.includes('validation')) {
      // Show field-specific errors to user
    }
  }
};
```

### 3. Agent Dashboard

```javascript
// Get agent's properties and statistics
const getAgentDashboard = async () => {
  try {
    const [myProperties, stats] = await Promise.all([
      api.request('/properties/my-properties/'),
      api.request('/properties/stats/')
    ]);
    
    return {
      properties: myProperties.results,
      statistics: stats,
      totalPages: Math.ceil(myProperties.count / 10)
    };
  } catch (error) {
    console.error('Dashboard load failed:', error.message);
    return null;
  }
};

// Filter agent properties by status
const getPropertiesByStatus = async (status) => {
  try {
    const properties = await api.request(`/properties/my-properties/?status=${status}`);
    return properties.results;
  } catch (error) {
    console.error('Failed to load properties:', error.message);
    return [];
  }
};
```

### 4. Property Updates

```javascript
// Update property
const updateProperty = async (propertyId, updates) => {
  try {
    const property = await api.request(`/properties/${propertyId}/`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
    
    console.log('Property updated:', property);
    return property;
  } catch (error) {
    console.error('Update failed:', error.message);
    throw error;
  }
};

// Delete property
const deleteProperty = async (propertyId) => {
  try {
    await api.request(`/properties/${propertyId}/`, {
      method: 'DELETE',
    });
    
    console.log('Property deleted successfully');
    return true;
  } catch (error) {
    console.error('Delete failed:', error.message);
    return false;
  }
};
```

## React Native Integration Examples

### 1. Property List Component

```jsx
import React, { useState, useEffect } from 'react';
import { FlatList, View, Text, TextInput, TouchableOpacity } from 'react-native';

const PropertyList = () => {
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState({});

  const loadProperties = async () => {
    setLoading(true);
    try {
      const response = await getProperties({
        search: searchText,
        ...filters
      });
      setProperties(response.results);
    } catch (error) {
      console.error('Failed to load properties:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProperties();
  }, [searchText, filters]);

  const renderProperty = ({ item }) => (
    <TouchableOpacity 
      style={styles.propertyCard}
      onPress={() => navigation.navigate('PropertyDetails', { id: item.property_id })}
    >
      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.price}>${item.price}</Text>
      <Text style={styles.location}>{item.location}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.searchInput}
        placeholder="Search properties..."
        value={searchText}
        onChangeText={setSearchText}
      />
      
      <FlatList
        data={properties}
        renderItem={renderProperty}
        keyExtractor={(item) => item.property_id.toString()}
        refreshing={loading}
        onRefresh={loadProperties}
      />
    </View>
  );
};
```

### 2. Property Creation Form

```jsx
import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView } from 'react-native';

const CreatePropertyForm = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    location: '',
    size: '',
    type: 'HOUSE',
    bedrooms: '',
    listing_type: 'SALE',
    amenity_ids: [],
    attraction_ids: [],
    is_featured: false
  });
  
  const [amenities, setAmenities] = useState([]);
  const [attractions, setAttractions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadOptions();
  }, []);

  const loadOptions = async () => {
    try {
      const options = await getPropertyOptions();
      setAmenities(options.amenities);
      setAttractions(options.attractions);
    } catch (error) {
      console.error('Failed to load options:', error);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const property = await createProperty({
        ...formData,
        price: parseFloat(formData.price),
        size: parseFloat(formData.size),
        bedrooms: parseInt(formData.bedrooms)
      });
      
      // Navigate to property details or back to list
      navigation.navigate('PropertyDetails', { id: property.property_id });
    } catch (error) {
      console.error('Failed to create property:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <TextInput
        style={styles.input}
        placeholder="Property Title"
        value={formData.title}
        onChangeText={(text) => setFormData({...formData, title: text})}
      />
      
      <TextInput
        style={styles.textArea}
        placeholder="Description"
        multiline
        numberOfLines={4}
        value={formData.description}
        onChangeText={(text) => setFormData({...formData, description: text})}
      />
      
      <TextInput
        style={styles.input}
        placeholder="Price"
        keyboardType="numeric"
        value={formData.price}
        onChangeText={(text) => setFormData({...formData, price: text})}
      />
      
      {/* Add more form fields as needed */}
      
      <TouchableOpacity 
        style={styles.submitButton}
        onPress={handleSubmit}
        disabled={loading}
      >
        <Text style={styles.submitButtonText}>
          {loading ? 'Creating...' : 'Create Property'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};
```

## Best Practices

### 1. Error Handling

```javascript
// Centralized error handling
const handleAPIError = (error) => {
  if (error.message.includes('401')) {
    // Redirect to login
    navigation.navigate('Login');
  } else if (error.message.includes('403')) {
    // Show permission denied message
    Alert.alert('Permission Denied', 'You do not have permission to perform this action.');
  } else if (error.message.includes('validation')) {
    // Handle validation errors
    const errors = JSON.parse(error.message);
    // Show field-specific errors
  } else {
    // Generic error message
    Alert.alert('Error', 'Something went wrong. Please try again.');
  }
};
```

### 2. Offline Support

```javascript
// Cache properties for offline viewing
import AsyncStorage from '@react-native-async-storage/async-storage';

const cacheProperties = async (properties) => {
  try {
    await AsyncStorage.setItem('cached_properties', JSON.stringify(properties));
  } catch (error) {
    console.error('Failed to cache properties:', error);
  }
};

const getCachedProperties = async () => {
  try {
    const cached = await AsyncStorage.getItem('cached_properties');
    return cached ? JSON.parse(cached) : [];
  } catch (error) {
    console.error('Failed to get cached properties:', error);
    return [];
  }
};
```

### 3. Image Optimization

```javascript
// Compress images before upload
import ImageResizer from 'react-native-image-resizer';

const compressImage = async (imageUri) => {
  try {
    const resized = await ImageResizer.createResizedImage(
      imageUri,
      800, // max width
      600, // max height
      'JPEG',
      80, // quality
      0, // rotation
      undefined, // output path
      false, // keep metadata
      { mode: 'contain', onlyScaleDown: true }
    );
    
    return resized.uri;
  } catch (error) {
    console.error('Image compression failed:', error);
    return imageUri;
  }
};
```

### 4. Performance Optimization

```javascript
// Implement pagination for large lists
const usePaginatedProperties = () => {
  const [properties, setProperties] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);

  const loadMore = async () => {
    if (loading || !hasMore) return;
    
    setLoading(true);
    try {
      const response = await getProperties({ page });
      
      if (page === 1) {
        setProperties(response.results);
      } else {
        setProperties(prev => [...prev, ...response.results]);
      }
      
      setHasMore(!!response.next);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Failed to load properties:', error);
    } finally {
      setLoading(false);
    }
  };

  return { properties, loadMore, loading, hasMore };
};
```

## Testing

### 1. Unit Tests

```javascript
// Test API client
import { PropertyAPI } from '../api/PropertyAPI';

describe('PropertyAPI', () => {
  let api;
  
  beforeEach(() => {
    api = new PropertyAPI();
    // Mock localStorage
    global.localStorage = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn()
    };
  });

  test('should make authenticated requests', async () => {
    localStorage.getItem.mockReturnValue('mock-token');
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: [] })
    });

    await api.request('/properties/');
    
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/properties/'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': 'Bearer mock-token'
        })
      })
    );
  });
});
```

### 2. Integration Tests

```javascript
// Test property creation flow
describe('Property Creation', () => {
  test('should create property with valid data', async () => {
    const propertyData = {
      title: 'Test Property',
      description: 'Test description',
      price: 100000,
      location: 'Test Location',
      size: 100,
      type: 'HOUSE',
      bedrooms: 2,
      listing_type: 'SALE'
    };

    const result = await createProperty(propertyData);
    
    expect(result).toHaveProperty('property_id');
    expect(result.title).toBe(propertyData.title);
    expect(result.status).toBe('PENDING');
  });
});
```

## Security Considerations

1. **Token Storage**: Use secure storage for JWT tokens
2. **Input Validation**: Validate all user inputs before sending to API
3. **HTTPS**: Always use HTTPS in production
4. **Rate Limiting**: Implement client-side rate limiting to prevent abuse
5. **Error Messages**: Don't expose sensitive information in error messages

## Performance Tips

1. **Lazy Loading**: Load property details only when needed
2. **Image Caching**: Cache property images for better performance
3. **Debounced Search**: Implement debounced search to reduce API calls
4. **Background Sync**: Sync data in background when app becomes active
5. **Optimistic Updates**: Update UI immediately, sync with server later

This integration guide provides a solid foundation for implementing property management functionality in mobile applications using the Broadleaf API.
