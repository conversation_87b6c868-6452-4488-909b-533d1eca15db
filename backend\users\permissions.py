from rest_framework import permissions

class IsAgent(permissions.BasePermission):
    """
    Custom permission to only allow agents to access the view.
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role.role_name == 'AGENT'

    def handle_no_permission(self):
        messages.error(self.request, "Only agents can perform this action.")
        return redirect('properties:property_list')

class IsClient(permissions.BasePermission):
    """
    Custom permission to only allow clients to access the view.
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role.role_name == 'CLIENT' 
