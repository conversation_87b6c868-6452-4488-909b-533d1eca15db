from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from rest_framework_simplejwt.tokens import RefreshToken
from users.models import User, Role

class JWTAuthenticationTest(APITestCase):
    def setUp(self):
        # Create a role
        self.role = Role.objects.create(role_name='CLIENT')
        
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            name='Test User',
            role=self.role
        )
        
        # Create a superuser
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            name='Admin User',
            role=self.role
        )

    def test_obtain_token(self):
        """Test obtaining JWT token"""
        url = reverse('token_obtain_pair')
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIsInstance(response.data['access'], str)
        self.assertIsInstance(response.data['refresh'], str)

    def test_obtain_token_invalid_credentials(self):
        """Test obtaining JWT token with invalid credentials"""
        url = reverse('token_obtain_pair')
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpass'
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_refresh_token(self):
        """Test refreshing JWT token"""
        # First get a token
        refresh = RefreshToken.for_user(self.user)
        
        # Then refresh it
        url = reverse('token_refresh')
        data = {
            'refresh': str(refresh)
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIsInstance(response.data['access'], str)

    def test_protected_endpoint_with_token(self):
        """Test accessing protected endpoint with valid token"""
        # Get token
        refresh = RefreshToken.for_user(self.user)
        access_token = str(refresh.access_token)
        
        # Set up authentication
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Try to access protected endpoint
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without token"""
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_protected_endpoint_with_invalid_token(self):
        """Test accessing protected endpoint with invalid token"""
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid_token')
        
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_superuser_token(self):
        """Test superuser token generation and access"""
        # Get token for superuser
        refresh = RefreshToken.for_user(self.superuser)
        access_token = str(refresh.access_token)
        
        # Set up authentication
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Try to access protected endpoint
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_token_expiration(self):
        """Test token expiration"""
        # Get token
        refresh = RefreshToken.for_user(self.user)
        access_token = str(refresh.access_token)
        
        # Set up authentication
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Access endpoint should work
        url = reverse('user-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # TODO: Add test for expired token once we have a way to manipulate token expiration
        # This would require modifying the JWT settings temporarily for the test 