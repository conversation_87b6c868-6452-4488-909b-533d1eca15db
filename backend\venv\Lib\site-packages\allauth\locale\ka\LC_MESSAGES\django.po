# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-03 04:26-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ka\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#: account/adapter.py:51
msgid "Username can not be used. Please use other username."
msgstr ""
"ამ მომხმარებლის სახელის გამოყენება შეუძლებელია. გთხოვთ გამოიყენოთ სხვა."

#: account/adapter.py:57
msgid "Too many failed login attempts. Try again later."
msgstr "შესვლის ძალიან ბევრი წარუმატებელი მცდელობა. მოგვიანებით სცადეთ."

#: account/adapter.py:59
msgid "A user is already registered with this email address."
msgstr "მომხმარებელი ამ ელ. ფოსტით უკვე დარეგისტრირებულია."

#: account/adapter.py:60
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "მიმდინარე პაროლი"

#: account/adapter.py:340
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "პაროლი უნდა შეიცავდეს მინიმუმ {0} სიმბოლოს."

#: account/apps.py:9
msgid "Accounts"
msgstr "მომხმარებლის ანგარიშები"

#: account/forms.py:61 account/forms.py:445
msgid "You must type the same password each time."
msgstr "თქვენ უნდა აკრიფოთ ერთი და იგივე პაროლი ყოველ ჯერზე."

#: account/forms.py:93 account/forms.py:408 account/forms.py:547
#: account/forms.py:685
msgid "Password"
msgstr "პაროლი"

#: account/forms.py:94
msgid "Remember Me"
msgstr "დამიმახსოვრე"

#: account/forms.py:98
msgid "This account is currently inactive."
msgstr "ეს ანგარიში ამჟამად გაუქმებულია."

#: account/forms.py:100
msgid "The email address and/or password you specified are not correct."
msgstr "თქვენს მიერ მითითებული ელ. ფოსტა ან პაროლი არასწორია."

#: account/forms.py:103
msgid "The username and/or password you specified are not correct."
msgstr "თქვენს მიერ მითითებული მომხმარებლის სახელი ან პაროლი არასწორია."

#: account/forms.py:114 account/forms.py:283 account/forms.py:472
#: account/forms.py:567
msgid "Email address"
msgstr "ელ. ფოსტა"

#: account/forms.py:118 account/forms.py:321 account/forms.py:469
#: account/forms.py:562
msgid "Email"
msgstr "ელ. ფოსტა"

#: account/forms.py:121 account/forms.py:124 account/forms.py:273
#: account/forms.py:276
msgid "Username"
msgstr "მომხმარებლის სახელი"

#: account/forms.py:134
msgid "Username or email"
msgstr "მომხმარებლის სახელი ან ელ. ფოსტა"

#: account/forms.py:137
msgctxt "field label"
msgid "Login"
msgstr "შესვლა"

#: account/forms.py:148
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "დაგავიწყდა პაროლი?"

#: account/forms.py:312
msgid "Email (again)"
msgstr "ელ. ფოსტა (გაამეორეთ)"

#: account/forms.py:316
msgid "Email address confirmation"
msgstr "ელ. ფოსტის დადასტურება"

#: account/forms.py:324
msgid "Email (optional)"
msgstr "ელ. ფოსტა (არ არის აუცილებელი)"

#: account/forms.py:379
msgid "You must type the same email each time."
msgstr "თქვენ უნდა ჩაწეროთ ერთი და იგივე ელ. ფოსტა ყოველ ჯერზე."

#: account/forms.py:414 account/forms.py:550
msgid "Password (again)"
msgstr "პაროლი (გაამეორეთ)"

#: account/forms.py:484
msgid "This email address is already associated with this account."
msgstr "ეს ელ.ფოსტის მისამართი უკვე დაკავშირებულია ამ ანგარიშთან."

#: account/forms.py:486
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "თქვენ არ შეგიძლიათ დაამატოთ %d- ზე მეტი ელექტრონული ფოსტის მისამართი."

#: account/forms.py:524
msgid "Current Password"
msgstr "მიმდინარე პაროლი"

#: account/forms.py:527 account/forms.py:634
msgid "New Password"
msgstr "ახალი პაროლი"

#: account/forms.py:530 account/forms.py:635
msgid "New Password (again)"
msgstr "ახალი პაროლი (გაამეორეთ)"

#: account/forms.py:538
msgid "Please type your current password."
msgstr "გთხოვთ აკრიფეთ მიმდინარე პაროლი."

#: account/forms.py:579
msgid "The email address is not assigned to any user account"
msgstr ""
"ეს ელექტრონული ფოსტის მისამართი არ არის მიბმული რომელიმე მომხმარებლის "
"ანგარიშზე"

#: account/forms.py:655
msgid "The password reset token was invalid."
msgstr "პაროლის აღდგენის კოდი არასწორია."

#: account/models.py:21
msgid "user"
msgstr "მომხმარებელი"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "ელ. ფოსტა"

#: account/models.py:28
msgid "verified"
msgstr "დადასტურებული"

#: account/models.py:29
msgid "primary"
msgstr "პირველადი"

#: account/models.py:35
msgid "email addresses"
msgstr "ელ. ფოსტის ანგარიშები"

#: account/models.py:141
msgid "created"
msgstr "შექმნილი"

#: account/models.py:142
msgid "sent"
msgstr "გაგზავნილი"

#: account/models.py:143 socialaccount/models.py:65
msgid "key"
msgstr "კოდი"

#: account/models.py:148
msgid "email confirmation"
msgstr "ელ. ფოსტის დადასტურება"

#: account/models.py:149
msgid "email confirmations"
msgstr "ელ. ფოსტის დადასტურებები"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:15 mfa/forms.py:17
msgid "Code"
msgstr ""

#: mfa/forms.py:48
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:32
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"მომხმარებელი უკვე არსებობს ამ ელ.ფოსტის მისამართით.%s-ით შესვლა ვერ "
"მოხერხდება."

#: socialaccount/adapter.py:138
msgid "Your account has no password set up."
msgstr "თქვენს ანგარიშს არ აქვს პაროლი დაყენებული."

#: socialaccount/adapter.py:145
msgid "Your account has no verified email address."
msgstr "თქვენს ანგარიშს არ აქვს დადასტურებული ელ. ფოსტა."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "სოციალური ანგარიშები"

#: socialaccount/models.py:39 socialaccount/models.py:93
msgid "provider"
msgstr "პროვაიდერი"

#: socialaccount/models.py:48
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "პროვაიდერი"

#: socialaccount/models.py:52
msgid "name"
msgstr "სახელი"

#: socialaccount/models.py:54
msgid "client id"
msgstr "კლიენტის id"

#: socialaccount/models.py:56
msgid "App ID, or consumer key"
msgstr "აპლიკაციის ID ან მომხმარებლის კოდი"

#: socialaccount/models.py:59
msgid "secret key"
msgstr "საიდუმლო კოდი"

#: socialaccount/models.py:62
msgid "API secret, client secret, or consumer secret"
msgstr ""
"API-ს საიდუმლო კოდი, კლიენტის საიდუმლო კოდი ან მომხმარებლის საიდუმლო კოდი"

#: socialaccount/models.py:65
msgid "Key"
msgstr "კოდი"

#: socialaccount/models.py:77
msgid "social application"
msgstr "სოციალური აპლიკაცია"

#: socialaccount/models.py:78
msgid "social applications"
msgstr "სოციალური აპლიკაციები"

#: socialaccount/models.py:113
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:115
msgid "last login"
msgstr "ბოლო შესვლის თარიღი"

#: socialaccount/models.py:116
msgid "date joined"
msgstr "ანგარიშის შექმნის თარიღი"

#: socialaccount/models.py:117
msgid "extra data"
msgstr "სხვა მონაცემები"

#: socialaccount/models.py:121
msgid "social account"
msgstr "სოციალური ანგარიში"

#: socialaccount/models.py:122
msgid "social accounts"
msgstr "სოციალური ანგარიშები"

#: socialaccount/models.py:156
msgid "token"
msgstr "კოდი"

#: socialaccount/models.py:157
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ან access token (OAuth2)"

#: socialaccount/models.py:161
msgid "token secret"
msgstr "საიდუმლო კოდი"

#: socialaccount/models.py:162
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ან refresh token (OAuth2)"

#: socialaccount/models.py:165
msgid "expires at"
msgstr "ვადა გაუსვლის თარიღი"

#: socialaccount/models.py:170
msgid "social application token"
msgstr "სოციალური ანგარიშის კოდი"

#: socialaccount/models.py:171
msgid "social application tokens"
msgstr "სოციალური ანგარიშების კოდი"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "პროფილის მონაცემები არასწორია"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "არასწორი პასუხი მოთხოვნის მიღებისას \"%s\"-დან."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "არასწორი პასუხი მოთხოვნის მიღებისას \"%s\"-დან."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "მოთხოვნის კოდი არ არის შენახული \"%s\" -სთვის."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "მოთხოვნის წვდომის კოდი არ არის შენახული \"%s\" -სთვის."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "\"%s\" - ზე კერძო რესურსებზე წვდომა არ არის."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "არასწორი პასუხი მოთხოვნის მიღებისას \"%s\"-დან."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "ანგარიში გაუქმებულია"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "ეს ანგარიში გაუქმებულია"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "ელ. ფოსტის მისამართები"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "შემდეგი ელ.ფოსტის მისამართები ასოცირდება თქვენს ანგარიშთან:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "დადასტურებული"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "არ არის დადასტურებელი"

#: templates/account/email.html:32
msgid "Primary"
msgstr "პირველადი"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "გახადე პირველადი"

#: templates/account/email.html:45 templates/account/email_change.html:29
msgid "Re-send Verification"
msgstr "ვერიფიკაციის თავიდან გაგზავნა"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "წაშლა"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "ელ. ფოსტის მისამართის დამატება"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "ელ. ფოსტის დამატება"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "ნამდვილად გსურთ წაშალოთ არჩეული ელ.ფოსტის მისამართი?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "მოგესალმებით %(site_name)s!-დან"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"მადლობა რომ იყენებ %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"თქვენ იღებთ ამ ელ. წერილს რადგან მომხმარებემა - %(user_display)s მიუთითა "
"თქვენი ელ.ფოსტის ანგარიში რომ დარეგისტრირდეს შემდეგ საიტზე - "
"%(site_domain)s.\n"
"\n"
"იმისთვის, რომ დაადასტუროთ ეს, გადადით შემდეგ ლინკზე %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "გთხოვთ დაადასტუროთ თქვენი ელ. ფოსტა"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"თქვენ იღებთ ამ ელ. წერილს რადგან თქვენ ან სხვა ვიღაცამ მოითხოვა პაროლის "
"შეცვლა თქვენს ანგარიშზე.\n"
"თქვენ შეგიძლიათ უბრალოდ დააიგნოროთ ეს ელ. წერილი თუ ეს თქვენი მოთხოვნა არ "
"იყო. დააჭირეთ ქვემოთ მოცემულ ლინკს პაროლის აღსადგენად."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "თუ თქვენ დაგავიწყდათ, თქვენი მომხმარებლის სახელია: %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "პაროლის აღდგენის ელ. წერილი"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"თქვენ იღებთ ამ ელ. წერილს რადგან თქვენ ან სხვა ვიღაცამ მოითხოვა პაროლის "
"შეცვლა თქვენს ანგარიშზე.\n"
"თქვენ შეგიძლიათ უბრალოდ დააიგნოროთ ეს ელ. წერილი თუ ეს თქვენი მოთხოვნა არ "
"იყო. დააჭირეთ ქვემოთ მოცემულ ლინკს პაროლის აღსადგენად."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "ელ. ფოსტის მისამართები"

#: templates/account/email_change.html:14
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "შემდეგი ელ.ფოსტის მისამართები ასოცირდება თქვენს ანგარიშთან:"

#: templates/account/email_change.html:19
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "თქვენი პირველადი ელ. ფოსტა უნდა იყოს დადასტურებული"

#: templates/account/email_change.html:38
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "დაადასტურეთ ელ. ფოსტა"

#: templates/account/email_change.html:49
#: templates/allauth/layouts/base.html:29
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "ელ. ფოსტა"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "დაადასტურეთ ელ. ფოსტა"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"გთხოვთ დაადასტურეთ, რომ <a href=\"mailto:%(email)s\">%(email)s</a> არის ელ. "
"ფოსტის მისამართი მომხმარებლისთვის: %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:28
msgid "Confirm"
msgstr "დადასტურება"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "ეს სოციალური ანგარიში უკვე მიბმულია სხვა მომხმარებლის ანგარიშთან."

#: templates/account/email_confirm.html:35
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"ეს ელ. ფოსტის დადასტურების ლინკი არასწორია ან ვადაგასულია. გთხოვთ <a href="
"\"%(email_url)s\">გააკეთოთ ახალი მოთხოვნა</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "შესვლა"

#: templates/account/login.html:12
#, fuzzy, python-format
#| msgid ""
#| "If you have not created an account yet, then please\n"
#| "<a href=\"%(signup_url)s\">sign up</a> first."
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"თუ არ გაქვთ შექმნილი ანგარიში, მაშინ გთხოვთ\n"
"<a href=\"%(signup_url)s\">დარეგისტრირდით</a>."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
msgid "Sign Out"
msgstr "გასვლა"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "დარწმუნებული ხარ, რომ გინდა გასვლა?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "შენ არ შეგიძლია წაშალო შენი პირველადი ელ. ფოსტა (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "ელ. ფოსტის დადასტურების წერილი გაგზავნილია %(email)s მისამართით."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "თქვენ დაადასტურეთ %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "ელ. ფოსტა %(email)s წაშლილია."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "თქვენ წარმატებით შეხვედით %(name)s-ად."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "თქვენ წარმატებით გახვედით თქვენი ანგარიშიდან."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "პაროლი წარმატებით შეცვლილია."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "პაროლი წარმატებით დაყენებულია."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "პირველადი ელ. ფოსტა წარმატებით დაყენებულია"

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "თქვენი პირველადი ელ. ფოსტა უნდა იყოს დადასტურებული"

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "პაროლის შეცვლა"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "დაგავიწყდა პაროლი?"

#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "პაროლის შეცვლა"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"დაგავიწყდათ თქვენი პაროლი? ჩაწერეთ თქვენი ელ. ფოსტა ქვემოთ და ჩვენ "
"გამოგიგზავნით ელ. წერილს აღსადგენად."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "ჩემი პაროლის შეცვლა"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr "გთხოვთ დაგვიკავშირდით თუ გაქვთ პრობლემა პაროლის შეცვლასთან."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"ჩვენ გამოგიგზავნეთ ელ. წერილი\n"
"ვერიფიკაციისთვის. გთხოვთ მიჰყევით მასში მოცემულ ლინკს. გთხოვთ\n"
"გთხოვთ დაგვიკავშირდით თუ არ მიიღებთ მას რამდენიმე წუთში."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "შეცდომა, ცუდი კოდი"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"პაროლის აღდგენის ლინკი არასწორია, ალბათ იმის გამო რომ უკვე გამოყენებული "
"იქნა. გთხოვთ მოითხოვეთ <a href=\"%(passwd_reset_url)s\">პაროლის შეცვლა "
"თავიდან</a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "თქვენი პაროლი შეცვლილია"

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "პაროლის დაყენება"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "დაადასტურეთ ელ. ფოსტა"

#: templates/account/reauthenticate.html:12
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "რეგისტრაცია"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "რეგისტრაცია"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "უკვე გაქვს ანგარიში? <a href=\"%(login_url)s\">შესვლა</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "რეგისტრაცია დროებით გაუქმებულია"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "ბოდიში, რეგისტრაცია დროებით გაუქმებულია"

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "შენიშვნა"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "თქვენ უკვე ხართ შესული როგორც %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "გაფრთხილება:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"თქვენ ამჟამად არ გაქვთ რაიმე ელ.ფოსტის მისამართი დაყენებული. უნდა დაამატოთ "
"ელექტრონული ფოსტის მისამართი, რათა მიიღოთ შეტყობინებები, აღადგინოთ პაროლი და "
"ა.შ."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "დაადასტურეთ თქვენი ელ. ფოსტა"

#: templates/account/verification_sent.html:12
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"ჩვენ გამოგიგზავნეთ ელ. წერილი ვერიფიკაციისთვის. მიჰყევით მასში მოცემულ "
"ლინკს, რომ დაასრულოთ რეგისტრაცია. გთხოვთ დაგვიკავშირდით თუ არ მიიღებთ მას "
"რამდენიმე წუთში."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"საიტის ეს ნაწილი ითხოვს იმის დადასტურებას, რომ\n"
"თქვენ ხართ ის, ვინც აცხადებთ, რომ ხართ. ამიტომ ჩვენ ვითხოვთ\n"
"თქვენი ელ. ფოსტის მისამართის საკუთრების დადასტურებას. "

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"ჩვენ გამოგიგზავნეთ ელ. წერილი\n"
"ვერიფიკაციისთვის, მიჰყევით მასში მოცემულ ლინკს. გთხოვთ\n"
"დაგვიკავშირდით თუ არ მიიღებთ მას რამდენიმე წუთში."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>შენიშვნა:</strong> თქვენ ჯერ კიდეგ შეგიძლიათ <a href=\"%(email_url)s"
"\">შეცვალოთ ელ. ფოსტის მისამართო</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:13 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:17
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:19
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:31 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:39 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:44 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:47
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:56
msgid "View"
msgstr ""

#: templates/mfa/index.html:62
msgid "Download"
msgstr ""

#: templates/mfa/index.html:70 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "საიდუმლო კოდი"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Social Network Login Failure"
msgstr "შეცდომა სოციალური ანგარიშით შესვლისას"

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "დაფიქსირდა შეცდომა სოციალური ანგარიშით შესვლის დროს."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "ანგარიშის კავშირები"

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"თქვენ შეგიძლიათ შეხვიდეთ თქვენს ანგარიშში რომელიმე შემდეგი სოციალური "
"ანგარიშის გამოყენებით:"

#: templates/socialaccount/connections.html:45
msgid ""
"You currently have no social network accounts connected to this account."
msgstr ""
"თქვენ ამჟამად არ გაქვთ ამ ანგარიშთან დაკავშირებული სოციალური ქსელის "
"ანგარიშები."

#: templates/socialaccount/connections.html:48
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "სოციალური ანგარიშის დამატება"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "%(provider)s ანგარიშის დაკავშირება"

#: templates/socialaccount/login.html:13
#, fuzzy, python-format
#| msgid ""
#| "You are about to connect a new third party account from %(provider)s."
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "თქვენ აპირებთ დააკავშიროთ %(provider)s ანგარიში."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "შესვლა %(provider)s ანგარიშით"

#: templates/socialaccount/login.html:20
#, fuzzy, python-format
#| msgid ""
#| "You are about to sign in using a third party account from %(provider)s."
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "თქვენ აპირებთ შესვლას %(provider)s ანგარიშით."

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "გაგრძელება"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "შესვლა გაუქმებულია"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"თქვენ გადაწყვიტეთ გააუქმოთ შესვლა ჩვენს საიტზე თქვენი ერთ-ერთი არსებული  "
"ანგარიშის გამოყენებით. თუ ეს შეცდომა იყო, გთხოვთ <a href=\"%(login_url)s"
"\">ხელახლა შედით</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "სოციალური ანგარიში მიბმულია."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "ეს სოციალური ანგარიში უკვე მიბმულია სხვა მომხმარებლის ანგარიშთან."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "სოციალური ანგარიში გაუქმებულია."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"თქვენ აპირებთ გამოიყენოთ თქვენი %(provider_name)s ანგარიში შესასვლელად\n"
"%(site_name)s-ზე. როგორც საბოლოო ნაბიჯი, გთხოვთ შეავსეთ ეს ფორმა:"

#: templates/socialaccount/snippets/login.html:8
msgid "Or use a third-party"
msgstr ""

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a href=\"%(signup_url)s"
#~ "\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "გთხოვთ შედით რომელიმე\n"
#~ "სოციალური ანგარიშით. ან, <a href=\"%(signup_url)s\">დარეგისტრირდით </a>\n"
#~ "ან შედით %(site_name)s საიტზე"

#~ msgid "or"
#~ msgstr "ან"

#~ msgid "change password"
#~ msgstr "პაროლის შეცვლა"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID-ით შესვლა"

#~ msgid "This email address is already associated with another account."
#~ msgstr "ეს ელ.ფოსტის მისამართი უკვე დაკავშირებულია სხვა ანგარიშთან."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "ჩვენ გამოგიგზავნეთ ელ. წერილი. გთხოვთ დაგვიკავშირდით თუ არ მიიღებთ "
#~ "რამდენიმე წუთში."
