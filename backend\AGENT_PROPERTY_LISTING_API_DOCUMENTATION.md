# Agent Property Listing API Documentation

This document provides comprehensive documentation for listing properties belonging to specific agents, including pagination, filtering, and integration examples for mobile applications.

## Overview

The Agent Property Listing API provides two main endpoints:
1. **My Properties** - For agents to view their own properties (all statuses)
2. **Agent Properties** - For viewing properties by any specific agent (public view shows only approved properties)

Both endpoints support comprehensive filtering, search functionality, and pagination.

## Authentication

All endpoints require authentication using JWT tokens:

```
Authorization: Bearer <your_jwt_token>
```

## Base URL

```
http://localhost:8000/api/properties/properties/
```

## Endpoints

### 1. My Properties (Current Agent's Properties)

**Endpoint:** `GET /api/properties/properties/my-properties/`

**Description:** List all properties created by the currently authenticated agent, regardless of approval status.

**Permission:** AGENT role required

**Query Parameters:**
- `search` (string): Search in title, description, and location
- `status` (string): Filter by property status (PENDING, APPROVED, REJECTED)
- `type` (string): Property type (HOUSE, APARTMENT, CONDO, LAND, COMMERCIAL)
- `listing_type` (string): SALE or RENT
- `min_price` (float): Minimum price filter
- `max_price` (float): Maximum price filter
- `bedrooms` (integer): Number of bedrooms
- `is_featured` (boolean): Filter featured properties
- `page` (integer): Page number for pagination (default: 1)
- `page_size` (integer): Items per page (default: 10, max: 100)

**Example Request:**
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8000/api/properties/properties/my-properties/?status=PENDING&page=1&page_size=5"
```

**Example Response:**
```json
{
  "count": 25,
  "next": "http://localhost:8000/api/properties/properties/my-properties/?page=2&page_size=5",
  "previous": null,
  "results": [
    {
      "property_id": 1,
      "title": "Modern Downtown Apartment",
      "description": "Beautiful 2-bedroom apartment with city views",
      "price": "250000.00",
      "location": "Downtown District",
      "size": "85.50",
      "type": "APARTMENT",
      "agent": {
        "user_id": 2,
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone_number": "+1234567890",
        "role_name": "AGENT",
        "profile_image_url": "http://localhost:8000/media/profile_images/john.jpg"
      },
      "bedrooms": 2,
      "listing_type": "SALE",
      "status": "PENDING",
      "primary_image": "http://localhost:8000/media/property_images/...",
      "is_featured": false,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 2. Agent Properties (Specific Agent's Properties)

**Endpoint:** `GET /api/properties/properties/agent-properties/`

**Description:** List properties created by a specific agent. Shows only approved properties to public users, but shows all properties if the requesting user is the same agent.

**Permission:** Authenticated users (any role)

**Required Query Parameters:**
- `agent_id` (integer, required): The user ID of the agent whose properties to list

**Optional Query Parameters:**
- `search` (string): Search in title, description, and location
- `type` (string): Property type (HOUSE, APARTMENT, CONDO, LAND, COMMERCIAL)
- `listing_type` (string): SALE or RENT
- `min_price` (float): Minimum price filter
- `max_price` (float): Maximum price filter
- `bedrooms` (integer): Number of bedrooms
- `is_featured` (boolean): Filter featured properties
- `page` (integer): Page number for pagination (default: 1)
- `page_size` (integer): Items per page (default: 10, max: 100)

**Example Request:**
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8000/api/properties/properties/agent-properties/?agent_id=2&type=APARTMENT&page=1"
```

**Example Response:**
```json
{
  "count": 12,
  "next": "http://localhost:8000/api/properties/properties/agent-properties/?agent_id=2&page=2",
  "previous": null,
  "results": [
    {
      "property_id": 5,
      "title": "Luxury Penthouse",
      "description": "Stunning penthouse with panoramic views",
      "price": "850000.00",
      "location": "Uptown District",
      "size": "200.00",
      "type": "APARTMENT",
      "agent": {
        "user_id": 2,
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone_number": "+1234567890",
        "role_name": "AGENT",
        "profile_image_url": "http://localhost:8000/media/profile_images/john.jpg"
      },
      "bedrooms": 3,
      "listing_type": "SALE",
      "status": "APPROVED",
      "primary_image": "http://localhost:8000/media/property_images/...",
      "is_featured": true,
      "created_at": "2024-01-10T14:20:00Z",
      "updated_at": "2024-01-12T09:15:00Z"
    }
  ]
}
```

## Integration Examples

### JavaScript/React Native

#### 1. Get Current Agent's Properties

```javascript
class AgentPropertyAPI {
  constructor(baseURL = 'http://localhost:8000/api/properties/properties') {
    this.baseURL = baseURL;
  }

  async getMyProperties(filters = {}, page = 1, pageSize = 10) {
    const token = localStorage.getItem('access_token');
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
      ...filters
    });

    const response = await fetch(`${this.baseURL}/my-properties/?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getAgentProperties(agentId, filters = {}, page = 1, pageSize = 10) {
    const token = localStorage.getItem('access_token');
    const params = new URLSearchParams({
      agent_id: agentId.toString(),
      page: page.toString(),
      page_size: pageSize.toString(),
      ...filters
    });

    const response = await fetch(`${this.baseURL}/agent-properties/?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }
}

// Usage Examples
const api = new AgentPropertyAPI();

// Get current agent's pending properties
const myPendingProperties = await api.getMyProperties({
  status: 'PENDING'
}, 1, 10);

// Get specific agent's apartment listings
const agentApartments = await api.getAgentProperties(123, {
  type: 'APARTMENT',
  listing_type: 'SALE'
}, 1, 20);
```

#### 2. React Native Component Example

```jsx
import React, { useState, useEffect } from 'react';
import { FlatList, View, Text, TouchableOpacity, TextInput } from 'react-native';

const AgentPropertiesScreen = ({ agentId, isOwnProperties = false }) => {
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filters, setFilters] = useState({});
  const [searchText, setSearchText] = useState('');

  const api = new AgentPropertyAPI();

  const loadProperties = async (pageNum = 1, reset = false) => {
    if (loading) return;
    
    setLoading(true);
    try {
      const response = isOwnProperties 
        ? await api.getMyProperties({ ...filters, search: searchText }, pageNum)
        : await api.getAgentProperties(agentId, { ...filters, search: searchText }, pageNum);
      
      if (reset) {
        setProperties(response.results);
      } else {
        setProperties(prev => [...prev, ...response.results]);
      }
      
      setHasMore(!!response.next);
      setPage(pageNum);
    } catch (error) {
      console.error('Failed to load properties:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProperties(1, true);
  }, [filters, searchText]);

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadProperties(page + 1);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const renderProperty = ({ item }) => (
    <TouchableOpacity style={styles.propertyCard}>
      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.price}>${item.price}</Text>
      <Text style={styles.location}>{item.location}</Text>
      <Text style={styles.status}>Status: {item.status}</Text>
      {isOwnProperties && (
        <View style={styles.statusBadge}>
          <Text style={[styles.statusText, { 
            color: item.status === 'APPROVED' ? 'green' : 
                   item.status === 'PENDING' ? 'orange' : 'red' 
          }]}>
            {item.status}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.searchInput}
        placeholder="Search properties..."
        value={searchText}
        onChangeText={setSearchText}
      />
      
      {/* Filter buttons */}
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => handleFilterChange('type', 'APARTMENT')}
        >
          <Text>Apartments</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => handleFilterChange('type', 'HOUSE')}
        >
          <Text>Houses</Text>
        </TouchableOpacity>
        {isOwnProperties && (
          <TouchableOpacity 
            style={styles.filterButton}
            onPress={() => handleFilterChange('status', 'PENDING')}
          >
            <Text>Pending</Text>
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={properties}
        renderItem={renderProperty}
        keyExtractor={(item) => item.property_id.toString()}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        refreshing={loading && page === 1}
        onRefresh={() => loadProperties(1, true)}
      />
    </View>
  );
};
```

### Python/Django Client Example

```python
import requests
from typing import Dict, List, Optional

class AgentPropertyClient:
    def __init__(self, base_url: str = "http://localhost:8000/api/properties/properties"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def set_auth_token(self, token: str):
        """Set the JWT authentication token"""
        self.session.headers.update({'Authorization': f'Bearer {token}'})
    
    def get_my_properties(self, filters: Dict = None, page: int = 1, page_size: int = 10) -> Dict:
        """Get current agent's properties"""
        params = {
            'page': page,
            'page_size': page_size,
            **(filters or {})
        }
        
        response = self.session.get(f"{self.base_url}/my-properties/", params=params)
        response.raise_for_status()
        return response.json()
    
    def get_agent_properties(self, agent_id: int, filters: Dict = None, page: int = 1, page_size: int = 10) -> Dict:
        """Get specific agent's properties"""
        params = {
            'agent_id': agent_id,
            'page': page,
            'page_size': page_size,
            **(filters or {})
        }
        
        response = self.session.get(f"{self.base_url}/agent-properties/", params=params)
        response.raise_for_status()
        return response.json()

# Usage example
client = AgentPropertyClient()
client.set_auth_token("your_jwt_token_here")

# Get agent's pending properties
pending_properties = client.get_my_properties({
    'status': 'PENDING',
    'type': 'APARTMENT'
}, page=1, page_size=20)

print(f"Found {pending_properties['count']} pending apartments")
for property in pending_properties['results']:
    print(f"- {property['title']}: ${property['price']}")
```

## Error Handling

### Common Error Responses

**400 Bad Request (Missing agent_id):**
```json
{
  "error": "agent_id parameter is required"
}
```

**400 Bad Request (Invalid agent_id):**
```json
{
  "error": "agent_id must be a valid integer"
}
```

**404 Not Found (Agent not found):**
```json
{
  "error": "Agent not found"
}
```

**401 Unauthorized:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

**403 Forbidden (Non-agent accessing my-properties):**
```json
{
  "detail": "You do not have permission to perform this action."
}
```

## Pagination Details

Both endpoints use cursor-based pagination with the following structure:

- `count`: Total number of items
- `next`: URL for the next page (null if no more pages)
- `previous`: URL for the previous page (null if first page)
- `results`: Array of property objects

**Pagination Parameters:**
- `page`: Page number (1-based)
- `page_size`: Items per page (default: 10, maximum: 100)

## Performance Considerations

1. **Caching**: Property lists are cached for 5 minutes to improve performance
2. **Pagination**: Always use pagination for large datasets
3. **Filtering**: Apply filters to reduce data transfer
4. **Image Loading**: Primary images are included as URLs, load them lazily in mobile apps

## Best Practices

1. **Error Handling**: Always implement proper error handling for network requests
2. **Loading States**: Show loading indicators during API calls
3. **Offline Support**: Cache property data locally for offline viewing
4. **Search Debouncing**: Implement debounced search to reduce API calls
5. **Infinite Scrolling**: Use pagination for smooth infinite scrolling experience

This API provides comprehensive property listing functionality for agents, supporting both personal property management and public property browsing with full filtering and pagination capabilities.
