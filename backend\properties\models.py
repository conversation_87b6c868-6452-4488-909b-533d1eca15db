from django.db import models
from django.utils import timezone
from users.models import User
import uuid
import os
from .storage import base64_storage
import base64
from django.core.files.base import ContentFile
from PIL import Image as PILImage
from io import BytesIO
from django.conf import settings
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache

def property_image_path(instance, filename):
    # This function is no longer needed but kept for migration compatibility
    return f"properties/{instance.property.property_id}/{filename}"

def compress_image(image, max_size=None, quality=None, optimize=None):
    """
    Compress an image while maintaining aspect ratio.
    Args:
        image: The image file to compress
        max_size: Maximum width and height (maintains aspect ratio)
        quality: JPEG quality (1-100)
        optimize: Whether to optimize the JPEG output
    Returns:
        BytesIO object containing the compressed image
    """
    # Get settings from Django settings
    compression_settings = settings.IMAGE_COMPRESSION
    max_size = max_size or compression_settings['MAX_SIZE']
    quality = quality or compression_settings['QUALITY']
    optimize = optimize if optimize is not None else compression_settings['OPTIMIZE']
    
    img = PILImage.open(image)
    
    # Convert to RGB if necessary (for PNG with transparency)
    if img.mode in ('RGBA', 'P'):
        img = img.convert('RGB')
    
    # Calculate new dimensions while maintaining aspect ratio
    if img.width > max_size[0] or img.height > max_size[1]:
        img.thumbnail(max_size, PILImage.Resampling.LANCZOS)
    
    # Save compressed image to BytesIO
    output = BytesIO()
    img.save(output, format='JPEG', quality=quality, optimize=optimize)
    output.seek(0)
    return output

class Property(models.Model):
    PROPERTY_TYPES = [
        ('HOUSE', 'House'),
        ('APARTMENT', 'Apartment'),
        ('CONDO', 'Condominium'),
        ('LAND', 'Land'),
        ('COMMERCIAL', 'Commercial')
    ]
    
    LISTING_TYPES = [
        ('SALE', 'For Sale'),
        ('RENT', 'For Rent')
    ]
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
    ]
    
    property_id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=12, decimal_places=2)
    location = models.CharField(max_length=255)
    size = models.DecimalField(max_digits=10, decimal_places=2, help_text="Size in square meters")
    type = models.CharField(max_length=20, choices=PROPERTY_TYPES, default='HOUSE')
    listing_type = models.CharField(max_length=10, choices=LISTING_TYPES, default='SALE')
    bedrooms = models.PositiveIntegerField(default=0)
    agent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='properties')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    amenities = models.ManyToManyField('Amenity', related_name='properties', blank=True)
    attractions = models.ManyToManyField('Attraction', related_name='properties', blank=True)
    is_featured = models.BooleanField(default=False, help_text="Mark property as featured for special display.")
    
    class Meta:
        verbose_name_plural = "Properties"
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def get_primary_image(self):
        primary = self.images.filter(is_primary=True).first()
        if primary:
            return primary
        # Return the first image if no primary image is set
        return self.images.first()

class Image(models.Model):
    image_id = models.AutoField(primary_key=True)
    property = models.ForeignKey('Property', on_delete=models.CASCADE, related_name='images')
    # Now use the storage instance
    image = models.ImageField(upload_to=property_image_path, storage=base64_storage)
    image_data = models.TextField(blank=True)
    caption = models.CharField(max_length=255, blank=True, null=True)
    is_primary = models.BooleanField(default=False)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-is_primary', '-uploaded_at']
    
    def save(self, *args, **kwargs):
        # If there's a file being uploaded, compress and convert it to base64
        if self.image and hasattr(self.image, 'read'):
            if settings.IMAGE_COMPRESSION['ENABLED']:
                # Compress the image
                compressed_image = compress_image(self.image)
                image_content = compressed_image.read()
            else:
                # Use original image without compression
                image_content = self.image.read()
            
            # Convert to base64
            image_b64 = base64.b64encode(image_content).decode('utf-8')
            content_type = 'image/jpeg' if settings.IMAGE_COMPRESSION['ENABLED'] else (
                self.image.content_type if hasattr(self.image, 'content_type') else 'image/jpeg'
            )
            self.image_data = f"data:{content_type};base64,{image_b64}"
            
            # Keep the filename in the image field
            self.image.name = self.image.name
        super().save(*args, **kwargs)

    def get_image_url(self):
        """Return the image URL, either base64 data or fallback to traditional URL"""
        if self.image_data:
            return self.image_data
        elif self.image:
            return self.image.url
        return None  # Or return a default image URL

class Inquiry(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('CONTACTED', 'Contacted'),
        ('VIEWED', 'Viewed'),
        ('CLOSED', 'Closed'),
    ]

    inquiry_id = models.AutoField(primary_key=True)
    property = models.ForeignKey(
        Property,
        on_delete=models.PROTECT,
        related_name='inquiries'
    )
    client = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='inquiries',
        limit_choices_to={'role__role_name': 'CLIENT'}
    )
    message = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_viewing_request = models.BooleanField(default=False)
    requested_date = models.DateField(null=True, blank=True)
    requested_time = models.TimeField(null=True, blank=True)
    viewing_status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('CONFIRMED', 'Confirmed'),
            ('RESCHEDULED', 'Rescheduled'),
            ('CANCELLED', 'Cancelled'),
            ('COMPLETED', 'Completed')
        ],
        default='PENDING'
    )

    class Meta:
        db_table = 'property_inquiries'
        verbose_name_plural = 'Inquiries'
        ordering = ['-created_at']

    def __str__(self):
        return f"Inquiry for {self.property.title} from {self.client.name}"

    def save(self, *args, **kwargs):
        # Ensure the client has the CLIENT role
        if not self.client.role.role_name == 'CLIENT':
            raise ValueError("Only users with CLIENT role can make inquiries")
        super().save(*args, **kwargs)

class Transaction(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    transaction_id = models.AutoField(primary_key=True)
    client = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transactions')
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name='transactions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    transaction_date = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Transaction {self.transaction_id} - {self.property.title} by {self.client.name}"

    class Meta:
        ordering = ['-transaction_date']

class Amenity(models.Model):
    name = models.CharField(max_length=100)
    icon = models.CharField(max_length=50, blank=True, help_text="CSS icon class (e.g., 'bi-check-circle-fill')")
    
    class Meta:
        verbose_name_plural = "Amenities"
        ordering = ['name']
    
    def __str__(self):
        return self.name

class Attraction(models.Model):
    name = models.CharField(max_length=100)
    distance = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, help_text="Distance in kilometers")
    icon = models.CharField(max_length=50, blank=True, help_text="CSS icon class (e.g., 'bi-check-circle-fill')")
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return self.name

class Favorite(models.Model):
    """Model for storing user's favorite properties"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites')
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name='favorited_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'property')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}'s favorite: {self.property.title}"

@receiver([post_save, post_delete], sender=Property)
def clear_property_cache(sender, instance, **kwargs):
    cache.delete(f"property_detail_{instance.pk}")
    cache.delete(f"web_property_detail_{instance.pk}")
    cache.delete_pattern("property_list_*")
    cache.delete_pattern("web_property_list_*")
    cache.delete_pattern("web_property_list_context_*")
    cache.delete_pattern(f"web_property_detail_context_{instance.pk}_*")

@receiver([post_save, post_delete], sender=Image)
def clear_image_cache(sender, instance, **kwargs):
    property_id = instance.property_id
    cache.delete(f"property_detail_{property_id}")
    cache.delete(f"web_property_detail_{property_id}")
    cache.delete_pattern("property_list_*")
    cache.delete_pattern("web_property_list_*")
    cache.delete_pattern("web_property_list_context_*")
    cache.delete_pattern(f"web_property_detail_context_{property_id}_*")
    cache.delete(f"primary_image_base64_{property_id}")
