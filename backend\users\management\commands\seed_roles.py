from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import Role
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Seeds the database with required user roles'

    @transaction.atomic
    def handle(self, *args, **kwargs):
        self.stdout.write('Seeding roles...')
        
        roles = [
            {
                'role_name': 'CLIENT',
                'description': 'Regular client user with basic access privileges'
            },
            {
                'role_name': 'AGENT',
                'description': 'Support agent with elevated access for handling client requests'
            },
            {
                'role_name': 'ADMIN',
                'description': 'Administrator with full system access'
            }
        ]

        for role_data in roles:
            role, created = Role.objects.get_or_create(
                role_name=role_data['role_name'],
                defaults={'description': role_data['description']}
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(
                    f'Successfully created role: {role.role_name}'
                ))
            else:
                self.stdout.write(self.style.WARNING(
                    f'Role already exists: {role.role_name}'
                ))

        self.stdout.write(self.style.SUCCESS('Role seeding completed!')) 