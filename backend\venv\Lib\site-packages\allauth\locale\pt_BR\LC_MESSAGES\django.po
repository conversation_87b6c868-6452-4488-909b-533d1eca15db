# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2013-2014
# <PERSON> <<EMAIL>>, 2013
# <PERSON> <<EMAIL>>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-03 04:26-0500\n"
"PO-Revision-Date: 2014-12-01 01:20+0000\n"
"Last-Translator: cacarrara <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/projects/p/"
"django-allauth/language/pt_BR/)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: account/adapter.py:51
msgid "Username can not be used. Please use other username."
msgstr "Este nome de usuário não pode ser usado. É preciso escolher outro."

#: account/adapter.py:57
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Excedida a quantidade de tentativas de login. Tente novamente mais tarde."

#: account/adapter.py:59
msgid "A user is already registered with this email address."
msgstr "Um usuário já foi registrado com este endereço de e-mail."

#: account/adapter.py:60
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Senha Atual"

#: account/adapter.py:340
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "A senha deve ter no mínimo {0} caracteres."

#: account/apps.py:9
msgid "Accounts"
msgstr "Contas"

#: account/forms.py:61 account/forms.py:445
msgid "You must type the same password each time."
msgstr "A mesma senha deve ser escrita em ambos os campos."

#: account/forms.py:93 account/forms.py:408 account/forms.py:547
#: account/forms.py:685
msgid "Password"
msgstr "Senha"

#: account/forms.py:94
msgid "Remember Me"
msgstr "Continuar logado"

#: account/forms.py:98
msgid "This account is currently inactive."
msgstr "Esta conta está desativada no momento."

#: account/forms.py:100
msgid "The email address and/or password you specified are not correct."
msgstr "O endereço de e-mail e/ou senha especificados não estão corretos."

#: account/forms.py:103
msgid "The username and/or password you specified are not correct."
msgstr "O nome de usuário e/ou senha especificados não estão corretos."

#: account/forms.py:114 account/forms.py:283 account/forms.py:472
#: account/forms.py:567
msgid "Email address"
msgstr "Endereço de e-mail"

#: account/forms.py:118 account/forms.py:321 account/forms.py:469
#: account/forms.py:562
msgid "Email"
msgstr "E-mail"

#: account/forms.py:121 account/forms.py:124 account/forms.py:273
#: account/forms.py:276
msgid "Username"
msgstr "Nome de usuário"

#: account/forms.py:134
msgid "Username or email"
msgstr "Nome de usuário ou e-mail"

#: account/forms.py:137
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:148
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Esqueceu a sua senha?"

#: account/forms.py:312
msgid "Email (again)"
msgstr "E-mail (novamente)"

#: account/forms.py:316
msgid "Email address confirmation"
msgstr "Confirmação de e-mail"

#: account/forms.py:324
msgid "Email (optional)"
msgstr "E-mail (opcional)"

#: account/forms.py:379
msgid "You must type the same email each time."
msgstr "O mesmo e-mail deve ser escrito em ambos os campos."

#: account/forms.py:414 account/forms.py:550
msgid "Password (again)"
msgstr "Senha (novamente)"

#: account/forms.py:484
msgid "This email address is already associated with this account."
msgstr "Este e-mail já está associado a esta conta."

#: account/forms.py:486
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Não se pode adicionar mais de %d endereços de e-mail."

#: account/forms.py:524
msgid "Current Password"
msgstr "Senha Atual"

#: account/forms.py:527 account/forms.py:634
msgid "New Password"
msgstr "Nova Senha"

#: account/forms.py:530 account/forms.py:635
msgid "New Password (again)"
msgstr "Nova Senha (novamente)"

#: account/forms.py:538
msgid "Please type your current password."
msgstr "Por favor insira a sua senha atual."

#: account/forms.py:579
msgid "The email address is not assigned to any user account"
msgstr "Este endereço de e-mail não está associado a nenhuma conta de usuário"

#: account/forms.py:655
msgid "The password reset token was invalid."
msgstr "Token de redefinição de senha inválido"

#: account/models.py:21
msgid "user"
msgstr "usuário"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "endereço de e-mail"

#: account/models.py:28
msgid "verified"
msgstr "verificado"

#: account/models.py:29
msgid "primary"
msgstr "primário"

#: account/models.py:35
msgid "email addresses"
msgstr "endereços de e-mail"

#: account/models.py:141
msgid "created"
msgstr "criado"

#: account/models.py:142
msgid "sent"
msgstr "enviado"

#: account/models.py:143 socialaccount/models.py:65
msgid "key"
msgstr "chave"

#: account/models.py:148
msgid "email confirmation"
msgstr "confirmação de e-mail"

#: account/models.py:149
msgid "email confirmations"
msgstr "confirmações de e-mail"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:15 mfa/forms.py:17
msgid "Code"
msgstr ""

#: mfa/forms.py:48
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:32
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Já existe uma conta com esse endereço de e-mail. Por favor, faça login na "
"conta existente e, em seguida, conecte com %s."

#: socialaccount/adapter.py:138
msgid "Your account has no password set up."
msgstr "Sua conta não tem uma senha definida."

#: socialaccount/adapter.py:145
msgid "Your account has no verified email address."
msgstr "Sua conta não tem um endereço de e-mail verificado."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Contas Sociais"

#: socialaccount/models.py:39 socialaccount/models.py:93
msgid "provider"
msgstr "provedor"

#: socialaccount/models.py:48
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "provedor"

#: socialaccount/models.py:52
msgid "name"
msgstr "nome"

#: socialaccount/models.py:54
msgid "client id"
msgstr "id do cliente"

#: socialaccount/models.py:56
msgid "App ID, or consumer key"
msgstr "App ID ou chave de consumidor"

#: socialaccount/models.py:59
msgid "secret key"
msgstr "Chave secreta"

#: socialaccount/models.py:62
msgid "API secret, client secret, or consumer secret"
msgstr "Segredo de API, segredo de cliente ou segredo de consumidor"

#: socialaccount/models.py:65
msgid "Key"
msgstr "Chave"

#: socialaccount/models.py:77
msgid "social application"
msgstr "aplicativo social"

#: socialaccount/models.py:78
msgid "social applications"
msgstr "aplicativos sociais"

#: socialaccount/models.py:113
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:115
msgid "last login"
msgstr "último acesso"

#: socialaccount/models.py:116
msgid "date joined"
msgstr "data de adesão"

#: socialaccount/models.py:117
msgid "extra data"
msgstr "dados extras"

#: socialaccount/models.py:121
msgid "social account"
msgstr "conta social"

#: socialaccount/models.py:122
msgid "social accounts"
msgstr "contas sociais"

#: socialaccount/models.py:156
msgid "token"
msgstr "token"

#: socialaccount/models.py:157
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ou token de acesso (OAuth2)"

#: socialaccount/models.py:161
msgid "token secret"
msgstr "token secret"

#: socialaccount/models.py:162
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ou token de atualização (OAuth2)"

#: socialaccount/models.py:165
msgid "expires at"
msgstr "expira em"

#: socialaccount/models.py:170
msgid "social application token"
msgstr "token de aplicativo social"

#: socialaccount/models.py:171
msgid "social application tokens"
msgstr "tokens de aplicativo social"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Dados pessoais inválidos"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Resposta inválida ao obter token de permissão de \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Resposta inválida ao obter token de acesso de \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nenhum token de permissão gravado para \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nenhum token de acesso gravado para \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Sem acesso a recursos privados de \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Resposta inválida ao obter token de permissão de \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Conta Inativa"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Esta conta está inativa."

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Endereços de E-mail"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "Os endereços de e-mail seguintes estão associados com a sua conta:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Verificado"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Não verificado"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Primário"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Definir como primário"

#: templates/account/email.html:45 templates/account/email_change.html:29
msgid "Re-send Verification"
msgstr "Re-enviar Verificação"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Remover"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Adicionar endereço de e-mail"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Adicionar e-mail"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Deseja mesmo remover o endereço de e-mail selecionado?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Olá do site %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Obrigado por usar o site %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Você está recebendo essa mensagem porque o usuário %(user_display)s utilizou "
"este e-mail para se cadastrar no site %(site_domain)s.\n"
"\n"
"Para confirmar que isso está correto, clique em %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Favor confirmar o endereço de e-mail"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Você está recebendo essa mensagem porque você ou alguém tentou requisitar "
"uma senha para sua conta de usuário no site %(site_domain)s.\n"
"Caso não tenha sido você que fez a requisição, simplesmente ignore a "
"mensagem. Do contrário, clique no link abaixo para redefinir a senha. "

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Caso tenha esquecido, seu nome de usuário é %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail de Redefinição de Senha"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You are receiving this e-mail because you or someone else has requested "
#| "a\n"
#| "password for your user account. However, we do not have any record of a "
#| "user\n"
#| "with email %(email)s in our database.\n"
#| "\n"
#| "This mail can be safely ignored if you did not request a password reset.\n"
#| "\n"
#| "If it was you, you can sign up for an account using the link below."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Você está recebendo essa mensagem porque você ou alguém tentou requisitar\n"
" uma senha para sua conta de usuário. Ocorre que não há nenhum usuário com "
"o\n"
" e-mail %(email)s cadastrado em nosso banco de dados.\n"
"\n"
"Caso não tenha sido você que fez a requisição, simplesmente ignore a "
"mensagem.\n"
" Do contrário, clique no link abaixo para se cadastrar. "

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "Endereços de E-mail"

#: templates/account/email_change.html:14
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Os endereços de e-mail seguintes estão associados com a sua conta:"

#: templates/account/email_change.html:19
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Seu endereço de e-mail principal precisa ser confirmado."

#: templates/account/email_change.html:38
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Confirmar Endereço de E-mail"

#: templates/account/email_change.html:49
#: templates/allauth/layouts/base.html:29
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirmar Endereço de E-mail"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Por favor confirme que <a href=\"mailto:%(email)s\">%(email)s</a> é um "
"endereço de e-mail do usuário %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:28
msgid "Confirm"
msgstr "Confirmar"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Esta conta social já está conectada a outra conta."

#: templates/account/email_confirm.html:35
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Este link de verificação de e-mail expirou ou é inválido. Por favor <a href="
"\"%(email_url)s\">peça uma nova verificação de e-mail.</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Entrar"

#: templates/account/login.html:12
#, fuzzy, python-format
#| msgid ""
#| "If you have not created an account yet, then please\n"
#| "<a href=\"%(signup_url)s\">sign up</a> first."
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Se você ainda não possui um cadastro, então, por favor\n"
"<a href=\"%(signup_url)s\">Cadastre-se</a> primeiro."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
msgid "Sign Out"
msgstr "Sair"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Tem certeza que deseja sair?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Você não pode remover o seu endereço principal de e-mail (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "E-mail de confirmação enviado para %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Você acaba de confirmar %(email)s"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s removido."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Login realizado com sucesso como usuário %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Você saiu do sistema."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Senha alterada com sucesso."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Senha definida com sucesso."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Endereço primário de e-mail configurado."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Seu endereço de e-mail principal precisa ser confirmado."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Alterar senha"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Esqueceu a sua senha?"

#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Redefinição da senha"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Esqueceu a sua senha? Insira seu endereço de e-mail abaixo para receber uma "
"mensagem com instruções sobre como alterá-la."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Redefinir a minha senha"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Por favor, entre em contato conosco caso tenha algum problema ao redefinir a "
"senha."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent you an e-mail. If you have not received it please check your "
#| "spam folder. Otherwise contact us if you do not receive it in a few "
#| "minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Acabamos de enviar um e-mail com as instruções. Caso não o receba nos "
"próximos minutos, verifique sua pasta de spam ou entre em contato conosco."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Problema no Token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"O link para redefinição da senha era inválido, provavelmente por já ter sido "
"usado. Por favor peça uma <a href=\"%(passwd_reset_url)s\">nova redefinição "
"da senha</a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "A sua senha foi alterada."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Definir senha"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Confirmar Endereço de E-mail"

#: templates/account/reauthenticate.html:12
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Cadastre-se"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Cadastro"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Já tem uma conta? Faça o <a href=\"%(login_url)s\">login</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Novos cadastros suspensos"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Infelizmente novos cadastros não podem ser feitos no momento."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "você já está logado como %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Aviso:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Não há nenhum endereço de e-mail associado a sua conta. Você deve adicionar "
"um e-mail para que possa receber notificações, redefinir a senha, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifique o seu endereço de e-mail"

#: templates/account/verification_sent.html:12
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. If you do not see the verification e-mail "
#| "in your main inbox, check your spam folder. Please contact us if you do "
#| "not receive the verification e-mail within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Acabamos de enviar uma mensagem contendo um link para finalizar o cadastro. "
"Caso não receba a mensagem, lembre de verificar a caixa de spam. Entre em "
"contato conosco se a mensagem não chegar dentro de alguns minutos."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Esta parte do site requer que verifiquemos\n"
"que você é quem diz que é. Para esse fim, pedimos que verifique que\n"
"é dono do seu endereço de e-mail. "

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside that e-mail. If you do not "
#| "see the verification e-mail in your main inbox, check your spam folder. "
#| "Otherwise\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Acabamos de enviar a você um e-mail de verificação.\n"
"Por favor clique no link dentro deste e-mail. Caso não receba a mensagem, \n"
"lembre de verificar a caixa de spam. Entre em contato conosco se a \n"
"mensagem não chegar dentro de alguns minutos."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> você ainda pode <a href=\"%(email_url)s\">alterar o "
"seu endereço de e-mail</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:13 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:17
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:19
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:31 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:39 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:44 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:47
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:56
msgid "View"
msgstr ""

#: templates/mfa/index.html:62
msgid "Download"
msgstr ""

#: templates/mfa/index.html:70 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "token secret"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Social Network Login Failure"
msgstr "Falha ao Entrar com Rede Social"

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "Houve um erro ao tentar entrar com a sua conta de rede social."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Conexões da Conta"

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Você pode entrar usando uma das seguintes contas externas:"

#: templates/socialaccount/connections.html:45
msgid ""
"You currently have no social network accounts connected to this account."
msgstr ""
"No momento não tem nenhuma conta de rede social conectada a esta conta."

#: templates/socialaccount/connections.html:48
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Adicionar uma Conta Externa"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Conecte com %(provider)s"

#: templates/socialaccount/login.html:13
#, fuzzy, python-format
#| msgid ""
#| "You are about to connect a new third party account from %(provider)s."
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Você está prestes a conectar uma nova conta externa do provedor %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Entre com %(provider)s"

#: templates/socialaccount/login.html:20
#, fuzzy, python-format
#| msgid ""
#| "You are about to sign in using a third party account from %(provider)s."
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Você está prestes a se logar usando uma conta externa do provedor "
"%(provider)s."

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "Continuar"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Entrada Cancelada"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Você decidiu cancelar a entrada no site usando uma de suas contas. Se mudar "
"de ideia, clique para <a href=\"%(login_url)s\">entrar</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "A conta social foi conectada."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Esta conta social já está conectada a outra conta."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "A conta social foi desconectada."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Você está prestes a usar sua conta do %(provider_name)s para acessar o\n"
"%(site_name)s. Para finalizar, por favor preencha o seguinte formulário:"

#: templates/socialaccount/snippets/login.html:8
msgid "Or use a third-party"
msgstr ""

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a href=\"%(signup_url)s"
#~ "\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Por favor, entre com uma\n"
#~ "de suas contas externas. Ou, <a href=\"%(signup_url)s\">Cadastre-se</a>\n"
#~ "em uma nova conta %(site_name)s e entre a seguir:"

#~ msgid "or"
#~ msgstr "ou"

#~ msgid "change password"
#~ msgstr "alterar a senha"

#~ msgid "OpenID Sign In"
#~ msgstr "Entrar com OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Este e-mail já está associado a outra conta."
