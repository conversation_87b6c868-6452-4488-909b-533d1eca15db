# Mobile Property Management API Documentation

This document describes the comprehensive property management API designed specifically for mobile applications, allowing AGENT users to create and manage properties with the same functionality available in the web app.

## Overview

The Mobile Property Management API provides full CRUD (Create, Read, Update, Delete) operations for properties, along with support for amenities, attractions, and images. This API is optimized for mobile applications with efficient data structures and mobile-friendly endpoints.

## Authentication

All endpoints require authentication using JWT tokens:

```
Authorization: Bearer <your_jwt_token>
```

**Note:** Only users with the AGENT role can create, update, and delete properties. All authenticated users can view approved properties.

## Base URL

```
http://localhost:8000/api/properties/
```

## Core Endpoints

### 1. Property Management

#### List Properties
**Endpoint:** `GET /api/properties/properties/`

**Description:** List all properties with advanced filtering and search capabilities

**Query Parameters:**
- `search` (string): Search in title, description, and location
- `min_price` (float): Minimum price filter
- `max_price` (float): Maximum price filter
- `type` (string): Property type (HOUSE, APARTMENT, CONDO, LAND, COMMERCIAL)
- `location` (string): Location filter
- `bedrooms` (integer): Number of bedrooms
- `listing_type` (string): SALE or RENT
- `is_featured` (boolean): Filter featured properties
- `page` (integer): Page number for pagination
- `page_size` (integer): Items per page (default: 10)

**Example Request:**
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8000/api/properties/properties/?search=apartment&min_price=100000&type=APARTMENT&page=1"
```

**Response:**
```json
{
  "count": 25,
  "next": "http://localhost:8000/api/properties/properties/?page=2",
  "previous": null,
  "results": [
    {
      "property_id": 1,
      "title": "Modern Apartment Downtown",
      "description": "Beautiful 2-bedroom apartment...",
      "price": "250000.00",
      "location": "Downtown District",
      "size": "85.50",
      "type": "APARTMENT",
      "agent": {
        "user_id": 2,
        "name": "John Smith",
        "email": "<EMAIL>",
        "role_name": "AGENT"
      },
      "bedrooms": 2,
      "listing_type": "SALE",
      "status": "APPROVED",
      "primary_image": "http://localhost:8000/media/...",
      "is_featured": false,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Get Property Details
**Endpoint:** `GET /api/properties/properties/{id}/`

**Description:** Get detailed information about a specific property including images, amenities, and attractions

**Response:**
```json
{
  "property_id": 1,
  "title": "Modern Apartment Downtown",
  "description": "Beautiful 2-bedroom apartment with city views...",
  "price": "250000.00",
  "location": "Downtown District",
  "size": "85.50",
  "type": "APARTMENT",
  "agent": {
    "user_id": 2,
    "name": "John Smith",
    "email": "<EMAIL>",
    "role_name": "AGENT"
  },
  "images": [
    {
      "image_id": 1,
      "image_url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "caption": "Living room",
      "is_primary": true,
      "uploaded_at": "2024-01-15T10:30:00Z"
    }
  ],
  "amenities": [
    {
      "id": 1,
      "name": "Swimming Pool",
      "icon": "bi-water"
    },
    {
      "id": 2,
      "name": "Gym",
      "icon": "bi-dumbbell"
    }
  ],
  "attractions": [
    {
      "id": 1,
      "name": "Shopping Mall",
      "distance": "0.50",
      "icon": "bi-shop"
    }
  ],
  "bedrooms": 2,
  "listing_type": "SALE",
  "status": "APPROVED",
  "is_featured": false,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Create Property (Mobile Optimized)
**Endpoint:** `POST /api/properties/properties/mobile-create/`

**Description:** Mobile-optimized endpoint for creating properties with comprehensive validation

**Permission:** AGENT role required

**Request Body:**
```json
{
  "title": "Beautiful Family Home",
  "description": "Spacious 3-bedroom house with garden and garage",
  "price": 350000.00,
  "location": "Suburban Area, City",
  "size": 150.75,
  "type": "HOUSE",
  "bedrooms": 3,
  "listing_type": "SALE",
  "amenity_ids": [1, 2, 5],
  "attraction_ids": [1, 3],
  "is_featured": false
}
```

**Response:**
```json
{
  "property_id": 15,
  "title": "Beautiful Family Home",
  "description": "Spacious 3-bedroom house with garden and garage",
  "price": "350000.00",
  "location": "Suburban Area, City",
  "size": "150.75",
  "type": "HOUSE",
  "agent": {
    "user_id": 2,
    "name": "John Smith",
    "email": "<EMAIL>",
    "role_name": "AGENT"
  },
  "images": [],
  "amenities": [
    {
      "id": 1,
      "name": "Swimming Pool",
      "icon": "bi-water"
    }
  ],
  "attractions": [
    {
      "id": 1,
      "name": "Shopping Mall",
      "distance": "0.50",
      "icon": "bi-shop"
    }
  ],
  "bedrooms": 3,
  "listing_type": "SALE",
  "status": "PENDING",
  "is_featured": false,
  "created_at": "2024-01-15T11:00:00Z",
  "updated_at": "2024-01-15T11:00:00Z"
}
```

#### Update Property
**Endpoint:** `PUT /api/properties/properties/{id}/` or `PATCH /api/properties/properties/{id}/`

**Description:** Update an existing property (agents can only update their own properties)

**Permission:** AGENT role required, must be property owner

**Request Body:** Same as create, but all fields are optional for PATCH

#### Delete Property
**Endpoint:** `DELETE /api/properties/properties/{id}/`

**Description:** Delete a property (agents can only delete their own properties)

**Permission:** AGENT role required, must be property owner

### 2. Agent-Specific Endpoints

#### Get Agent's Properties
**Endpoint:** `GET /api/properties/properties/my-properties/`

**Description:** List all properties created by the current agent, regardless of approval status

**Permission:** AGENT role required

**Query Parameters:** Same as property list endpoint, plus:
- `status` (string): Filter by property status (PENDING, APPROVED, REJECTED)

#### Get Agent Statistics
**Endpoint:** `GET /api/properties/properties/stats/`

**Description:** Get property statistics for the current agent

**Permission:** AGENT role required

**Response:**
```json
{
  "total_properties": 15,
  "pending_properties": 3,
  "approved_properties": 10,
  "rejected_properties": 2,
  "featured_properties": 5
}
```

### 3. Amenities and Attractions

#### List Amenities
**Endpoint:** `GET /api/properties/amenities/`

**Description:** Get all available amenities for property creation

**Response:**
```json
[
  {
    "id": 1,
    "name": "Swimming Pool",
    "icon": "bi-water"
  },
  {
    "id": 2,
    "name": "Gym",
    "icon": "bi-dumbbell"
  }
]
```

#### List Attractions
**Endpoint:** `GET /api/properties/attractions/`

**Description:** Get all available attractions for property creation

**Response:**
```json
[
  {
    "id": 1,
    "name": "Shopping Mall",
    "distance": "0.50",
    "icon": "bi-shop"
  },
  {
    "id": 2,
    "name": "School",
    "distance": "1.20",
    "icon": "bi-book"
  }
]
```

## Property Types

- `HOUSE`: Single-family house
- `APARTMENT`: Apartment unit
- `CONDO`: Condominium
- `LAND`: Land/lot
- `COMMERCIAL`: Commercial property

## Listing Types

- `SALE`: Property for sale
- `RENT`: Property for rent

## Property Status

- `PENDING`: Awaiting approval
- `APPROVED`: Approved and visible to all users
- `REJECTED`: Rejected by admin

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": "Invalid data provided",
  "details": {
    "price": ["This field must be greater than 0"],
    "title": ["This field is required"]
  }
}
```

**401 Unauthorized:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

**403 Forbidden:**
```json
{
  "detail": "You do not have permission to perform this action."
}
```

**404 Not Found:**
```json
{
  "detail": "Not found."
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- 100 requests per minute for authenticated users
- 1000 requests per hour for authenticated users

## Best Practices

1. **Image Optimization**: Compress images before uploading to improve performance
2. **Pagination**: Always use pagination for list endpoints to improve performance
3. **Caching**: Property details are cached for 15 minutes to improve response times
4. **Error Handling**: Implement proper error handling for all API calls
5. **Offline Support**: Cache property data locally for offline viewing

## Next Steps

After implementing the basic property management:
1. Add image upload functionality using the Image Upload API
2. Implement property favorites using the Favorites API
3. Add inquiry management for client-agent communication
4. Implement push notifications for property status updates
