from rest_framework import serializers
from properties.models import Property, Image, Inquiry, Transaction
from drf_spectacular.utils import extend_schema_field
from typing import Optional

class ImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()
    image_data = serializers.CharField(read_only=True)
    
    class Meta:
        model = Image
        fields = [
            'image_id', 'property', 'image', 'image_data', 'image_url', 
            'caption', 'is_primary', 'uploaded_at'
        ]
        read_only_fields = ['image_id', 'uploaded_at', 'image_data', 'image_url']
    
    @extend_schema_field(serializers.CharField(allow_null=True))
    def get_image_url(self, obj) -> Optional[str]:
        """Return the image URL or base64 data"""
        return obj.get_image_url()
    
    def validate_image(self, value):
        """Validate uploaded image file"""
        if value:
            # Check file size (max 10MB)
            if value.size > 10 * 1024 * 1024:
                raise serializers.ValidationError("Image file size must be less than 10MB")
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(value, 'content_type') and value.content_type not in allowed_types:
                raise serializers.ValidationError("Only JPEG, PNG, GIF, and WebP images are allowed")
        
        return value
    
    def validate_property(self, value):
        """Ensure the property belongs to the current user (agent)"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            if not value.agent == request.user:
                raise serializers.ValidationError("You can only upload images to your own properties")
        return value

class PropertyImageUploadSerializer(serializers.Serializer):
    """Serializer for bulk image uploads"""
    images = serializers.ListField(
        child=serializers.ImageField(),
        max_length=10,  # Maximum 10 images per upload
        help_text="List of image files to upload"
    )
    captions = serializers.ListField(
        child=serializers.CharField(max_length=255, required=False),
        required=False,
        help_text="Optional captions for the images"
    )
    property_id = serializers.IntegerField(help_text="ID of the property to upload images for")
    
    def validate_images(self, value):
        """Validate the list of images"""
        if not value:
            raise serializers.ValidationError("At least one image must be provided")
        
        for image in value:
            # Check file size (max 10MB per image)
            if image.size > 10 * 1024 * 1024:
                raise serializers.ValidationError(f"Image {image.name} file size must be less than 10MB")
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(image, 'content_type') and image.content_type not in allowed_types:
                raise serializers.ValidationError(f"Image {image.name} must be JPEG, PNG, GIF, or WebP")
        
        return value
    
    def validate_property_id(self, value):
        """Validate that the property exists and belongs to the current user"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            try:
                property_obj = Property.objects.get(property_id=value, agent=request.user)
                return property_obj
            except Property.DoesNotExist:
                raise serializers.ValidationError("Property not found or you don't have permission to upload images to it")
        return value