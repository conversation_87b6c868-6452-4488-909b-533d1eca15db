# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-03 04:26-0500\n"
"PO-Revision-Date: 2020-10-15 19:56+0200\n"
"Last-Translator: Anonymous User <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Translated-Using: django-rosetta 0.7.6\n"

#: account/adapter.py:51
msgid "Username can not be used. Please use other username."
msgstr "Käyttäjänimeä ei voi käyttää. Valitse toinen käyttäjänimi."

#: account/adapter.py:57
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Liian monta virheellistä kirjautumisyritystä. Yritä myöhemmin uudelleen."

#: account/adapter.py:59
msgid "A user is already registered with this email address."
msgstr "Tämä sähköpostiosoite on jo käytössä."

#: account/adapter.py:60
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Nykyinen salasana"

#: account/adapter.py:340
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Salasanan tulee olla vähintään {0} merkkiä pitkä."

#: account/apps.py:9
msgid "Accounts"
msgstr "Tili"

#: account/forms.py:61 account/forms.py:445
msgid "You must type the same password each time."
msgstr "Salasanojen tulee olla samat."

#: account/forms.py:93 account/forms.py:408 account/forms.py:547
#: account/forms.py:685
msgid "Password"
msgstr "Salasana"

#: account/forms.py:94
msgid "Remember Me"
msgstr "Muista minut"

#: account/forms.py:98
msgid "This account is currently inactive."
msgstr "Tämä tili on poistettu käytöstä."

#: account/forms.py:100
msgid "The email address and/or password you specified are not correct."
msgstr "Annettu sähköposti tai salasana ei ole oikein."

#: account/forms.py:103
msgid "The username and/or password you specified are not correct."
msgstr "Annettu käyttäjänimi tai salasana ei ole oikein."

#: account/forms.py:114 account/forms.py:283 account/forms.py:472
#: account/forms.py:567
msgid "Email address"
msgstr "Sähköpostiosoite"

#: account/forms.py:118 account/forms.py:321 account/forms.py:469
#: account/forms.py:562
msgid "Email"
msgstr "Sähköposti"

#: account/forms.py:121 account/forms.py:124 account/forms.py:273
#: account/forms.py:276
msgid "Username"
msgstr "Käyttäjänimi"

#: account/forms.py:134
msgid "Username or email"
msgstr "Käyttäjänimi tai sähköposti"

#: account/forms.py:137
msgctxt "field label"
msgid "Login"
msgstr "Käyttäjätunnus"

#: account/forms.py:148
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Salasana unohtunut?"

#: account/forms.py:312
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "Sähköpostiosoite (valinnainen)"

#: account/forms.py:316
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "sähköpostivarmistus"

#: account/forms.py:324
msgid "Email (optional)"
msgstr "Sähköpostiosoite (valinnainen)"

#: account/forms.py:379
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "Salasanojen tulee olla samat."

#: account/forms.py:414 account/forms.py:550
msgid "Password (again)"
msgstr "Salasana (uudestaan)"

#: account/forms.py:484
msgid "This email address is already associated with this account."
msgstr "Sähköpostiosoite on jo liitetty tähän tilliin."

#: account/forms.py:486
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Tiliisi ei ole liitetty vahvistettua sähköpostiosoitetta."

#: account/forms.py:524
msgid "Current Password"
msgstr "Nykyinen salasana"

#: account/forms.py:527 account/forms.py:634
msgid "New Password"
msgstr "Uusi salasana"

#: account/forms.py:530 account/forms.py:635
msgid "New Password (again)"
msgstr "Uusi salasana (uudestaan)"

#: account/forms.py:538
msgid "Please type your current password."
msgstr "Ole hyvä ja anna nykyinen salasanasi."

#: account/forms.py:579
msgid "The email address is not assigned to any user account"
msgstr "Sähköpostiosoite ei vastaa yhtäkään käyttäjätiliä."

#: account/forms.py:655
msgid "The password reset token was invalid."
msgstr "Salasanan uusimistarkiste ei kelpaa."

#: account/models.py:21
msgid "user"
msgstr "käyttäjä"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "sähköpostiosoite"

#: account/models.py:28
msgid "verified"
msgstr "vahvistettu"

#: account/models.py:29
msgid "primary"
msgstr "ensisijainen"

#: account/models.py:35
msgid "email addresses"
msgstr "sähköpostiosoitteet"

#: account/models.py:141
msgid "created"
msgstr "luotu"

#: account/models.py:142
msgid "sent"
msgstr "lähetetty"

#: account/models.py:143 socialaccount/models.py:65
msgid "key"
msgstr "avain"

#: account/models.py:148
msgid "email confirmation"
msgstr "sähköpostivarmistus"

#: account/models.py:149
msgid "email confirmations"
msgstr "sähköpostivarmistukset"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:15 mfa/forms.py:17
msgid "Code"
msgstr ""

#: mfa/forms.py:48
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:32
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Sähköpostiosoite on jo liitetty olemassaolevaan tiliin. Kirjaudu ensin "
"kyseiseen tiliin ja liitä %s-tilisi vasta sitten."

#: socialaccount/adapter.py:138
msgid "Your account has no password set up."
msgstr "Tilillesi ei ole asetettu salasanaa."

#: socialaccount/adapter.py:145
msgid "Your account has no verified email address."
msgstr "Tiliisi ei ole liitetty vahvistettua sähköpostiosoitetta."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Sosiaalisen median tilit"

#: socialaccount/models.py:39 socialaccount/models.py:93
msgid "provider"
msgstr "tarjoaja"

#: socialaccount/models.py:48
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "tarjoaja"

#: socialaccount/models.py:52
msgid "name"
msgstr "nimi"

#: socialaccount/models.py:54
msgid "client id"
msgstr "asiakas id"

#: socialaccount/models.py:56
msgid "App ID, or consumer key"
msgstr "Sovellus ID tai kuluttajan avain"

#: socialaccount/models.py:59
msgid "secret key"
msgstr "salainen avain"

#: socialaccount/models.py:62
msgid "API secret, client secret, or consumer secret"
msgstr "API:n, asiakkaan tai kuluttajan salaisuus"

#: socialaccount/models.py:65
msgid "Key"
msgstr "Avain"

#: socialaccount/models.py:77
msgid "social application"
msgstr "sosiaalinen applikaatio"

#: socialaccount/models.py:78
msgid "social applications"
msgstr "sosiaaliset applikaatiot"

#: socialaccount/models.py:113
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:115
msgid "last login"
msgstr "viimeisin sisäänkirjautuminen"

#: socialaccount/models.py:116
msgid "date joined"
msgstr "liittymispäivämäärä"

#: socialaccount/models.py:117
msgid "extra data"
msgstr "lisätiedot"

#: socialaccount/models.py:121
msgid "social account"
msgstr "sosiaalisen median tili"

#: socialaccount/models.py:122
msgid "social accounts"
msgstr "sosiaalisen median tilit"

#: socialaccount/models.py:156
msgid "token"
msgstr ""

#: socialaccount/models.py:157
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:161
msgid "token secret"
msgstr ""

#: socialaccount/models.py:162
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "expires at"
msgstr "vanhenee"

#: socialaccount/models.py:170
msgid "social application token"
msgstr ""

#: socialaccount/models.py:171
msgid "social application tokens"
msgstr ""

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Virheellinen vastaus palvelusta \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Virhe hankittaessa käyttöoikeustunnistetta palvelusta \"%s\""

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr ""

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr ""

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr ""

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Virheellinen vastaus palvelusta \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Tili poissa käytöstä"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Tämä tili ei ole käytössä."

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Sähköpostiosoitteet"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "Seuraavat sähköpostiosoitteet on liitetty tiliisi:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Vahvistettu"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Vahvistamaton"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Ensisijainen"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Aseta ensisijaiseksi"

#: templates/account/email.html:45 templates/account/email_change.html:29
msgid "Re-send Verification"
msgstr "Lähetä vahvistus uudelleen"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Poista"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Lisää sähköpostiosoite"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Lisää sähköposti"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Haluatko varmasti poistaa valitun sähköpostiosoitteen?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Terve palvelusta %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Kiitos, kun käytät %(site_name)s palvelua!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Sait tämän viestin, koska käyttäjä %(user_display)s palvelusta "
"%(site_domain)s antoi sähköpostiosoitteesi liitettäväksi tiliinsä.\n"
"\n"
"Vahvistaaksesi tiedot oikeiksi mene osoitteeseen %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Vahvista sähköpostiosoitteesi"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Sait tämän sähköpostin, koska sinä tai joku muu on pyytänyt salasasi "
"uusimista palvelussa %(site_domain)s.\n"
"Tämän viestin voi jättää huomiotta, jos et pyytänyt salasanan uusimista. "
"Klikkaa alla olevaa linkkiä uusiaksesi salasanasi."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Muistathan, että käyttäjätunnuksesi on %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Salasanan uusimissähköposti"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Sait tämän sähköpostin, koska sinä tai joku muu on pyytänyt salasasi "
"uusimista palvelussa %(site_domain)s.\n"
"Tämän viestin voi jättää huomiotta, jos et pyytänyt salasanan uusimista. "
"Klikkaa alla olevaa linkkiä uusiaksesi salasanasi."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "Sähköpostiosoitteet"

#: templates/account/email_change.html:14
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Seuraavat sähköpostiosoitteet on liitetty tiliisi:"

#: templates/account/email_change.html:19
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Ensisijaisen sähköpostiosoiteen tulee olla vahvistettu."

#: templates/account/email_change.html:38
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Vahvista sähköpostiosoite"

#: templates/account/email_change.html:49
#: templates/allauth/layouts/base.html:29
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "Sähköposti"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Vahvista sähköpostiosoite"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Vahvista, että <a href=\"mailto:%(email)s\">%(email)s</a> on käyttäjän "
"%(user_display)s sähköpostiosoite."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:28
msgid "Confirm"
msgstr "Vahvista"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Sosiaalisen median tili on jo liitetty toiseen tiliin."

#: templates/account/email_confirm.html:35
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Tämä sähköpostiosoitteen vahvistuslinkki on vanhentunut tai muuten "
"käyttökelvoton. Voit kuitenkin <a href=\"%(email_url)s\">pyytää uuden "
"vahvistuslinkin sähköpostiosoitteellesi</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Kirjaudu sisään"

#: templates/account/login.html:12
#, fuzzy, python-format
#| msgid ""
#| "If you have not created an account yet, then please\n"
#| "<a href=\"%(signup_url)s\">sign up</a> first."
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Jos et ole luonut vielä tiliä, niin <a href=\"%(signup_url)s\">rekisteröidy</"
"a> ensin."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
msgid "Sign Out"
msgstr "Kirjaudu ulos"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Haluatko varmasti kirjautua ulos?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Et voi poistaa ensisijaista sähköpostiosoitettasi (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Vahvistusviesti on lähetetty osoitteeseen %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Sähköpostiosoite %(email)s on vahvistettu."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Poistettiin sähköpostiosoite %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Kirjauduttiin sisään käyttäjänä %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Kirjauduit ulos."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Salasana vaihto onnistui."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Salasana asetettiin."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Ensisijainen sähköpostiosoite asetettiin."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ensisijaisen sähköpostiosoiteen tulee olla vahvistettu."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Vaihda salasana"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Salasana unohtunut?"

#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Salasanan uusiminen"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Unohditko salasanasi? Anna sähköpostiosoitteesi ja lähetämme sinulle "
"sähköpostin, jonka avulla voit uusia sen."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Salasanan uusiminen"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Ota meihin yhteyttä, jos sinulla on ongelmia salasanasi uusimisessa."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Olemme lähettäneet sinulle sähköpostivahvistuksen. Klikkaa sähköpostissa "
"olevaa linkkiä vahvistaaksesi sähköpostiosoitteesi. Ota meihin yhteyttä, jos "
"et saanut vahvistusviestiä muutaman minuutin sisällä."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Virheellinen tunniste"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Salasanan uusimislinkki ei toiminut. Tämä voi tapahtua, jos linkki on jo "
"käytetty. Voit kuitenkin <a href=\"%(passwd_reset_url)s\">uusia salasanan "
"uusimisen</a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Salasanasi on nyt vaihdettu."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Aseta salasana"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Vahvista sähköpostiosoite"

#: templates/account/reauthenticate.html:12
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Rekisteröidy"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Rekisteröidy"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Onko sinulla jo tili? <a href=\"%(login_url)s\">Kirjaudu sisään</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Rekisteröityminen on poissa käytöstä."

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Valitettavasti rekisteröityminen on pois käytöstä."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Huomio"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "olet jo kirjautunut käyttäjänä %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Varoitus:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Et ole asettanut sähköpostiosoitetta. Tämä tulisi tehdä, jotta voit saada "
"ilmoituksia, uusia salasanasi jne."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Vahvista sähköpostiosoitteesi"

#: templates/account/verification_sent.html:12
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Lähetimme sinulle sähköpostin vahvistusviestin. Klikkaa sähköpostissa olevaa "
"linkkiä viimeistelläksesi rekisteröitymisprosessin. Ota meihin yhteyttä, jos "
"et saanut vahvistusviestiä muutaman minuutin sisällä."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Tämä osa palvelua vaatii, että tiedämme kuka olet. Tämän takia sinun pitää "
"vahvistaa omistavasi ilmoittamasi sähköpostiosoite."

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Olemme lähettäneet sinulle sähköpostivahvistuksen. Klikkaa sähköpostissa "
"olevaa linkkiä vahvistaaksesi sähköpostiosoitteesi. Ota meihin yhteyttä, jos "
"et saanut vahvistusviestiä muutaman minuutin sisällä."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Huomio:</strong> voit edelleen <a href=\"%(email_url)s\">vaihtaa "
"sähköpostiosoitteesi</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:13 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:17
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:19
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:31 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:39 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:44 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:47
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:56
msgid "View"
msgstr ""

#: templates/mfa/index.html:62
msgid "Download"
msgstr ""

#: templates/mfa/index.html:70 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Social Network Login Failure"
msgstr "Sosiaalisen median tilillä kirjautuminen epäonnistui"

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Tapahtui virhe yritettäessä kirjautua käyttäen sosiaalisen median tiliä."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Liitetyt tilit"

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Voit kirjautua käyttäen seuraavia kirjautumispalveluita:"

#: templates/socialaccount/connections.html:45
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Tiliisi ei ole liitetty yhtäkään sosiaalisen median tiliä."

#: templates/socialaccount/connections.html:48
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Lisää kolmannen osapuolen tili"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Sisäänkirjautuminen keskeytettiin"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Keskeytit sisäänkirjautumisen olemassaolevalle tilillesi. Jos tämä oli "
"vahinko niin <a href=\"%(login_url)s\">kirjaudu sisään</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Sosiaalisen median tili liitettiin."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Sosiaalisen median tili on jo liitetty toiseen tiliin."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Sosiaalisen median tili on poistettu käytöstä."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Olet aikeissa käyttää %(provider_name)s-tiliäsi kirjautuaksesi palveluun\n"
"%(site_name)s. Täytä vielä seuraava lomake:"

#: templates/socialaccount/snippets/login.html:8
msgid "Or use a third-party"
msgstr ""

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a href=\"%(signup_url)s"
#~ "\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Kirjaudu sisään käyttäen kirjautumispalvelua tai <a href=\"%(signup_url)s"
#~ "\">rekisteröi</a> %(site_name)s-tili ja kirjaudu sisään alla olevalla "
#~ "lomakkeella:"

#~ msgid "or"
#~ msgstr "tai"

#~ msgid "change password"
#~ msgstr "vaihda salasanaa"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID kirjautuminen"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Sähköpostiosoite on jo liitetty toiseen tiliin."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Olemme lähettäneet sinulle sähköpostia. Ota meihin yhteyttä, jos et saa "
#~ "sitä muutaman minuutin sisällä."

#~ msgid "Account"
#~ msgstr "Tili"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Kirjautumistiedot eivät ole oikein."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Käyttäjänimi saa sisältää vain kirjaimia, numeroita ja erikoismerkkejä "
#~ "@/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Käyttäjänimi on käytössä. Valitse toinen käyttäjänimi."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Kirjaudu sisään"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Olet vahvistanut, että <a href=\"mailto:%(email)s\">%(email)s</a> on "
#~ "sähköpostiosoite käyttäjälle %(user_display)s."
