# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2013.
# <PERSON><PERSON> <<EMAIL>>, 2018.
# Filip <PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.55\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-03 04:26-0500\n"
"PO-Revision-Date: 2023-09-06 14:00+0200\n"
"Last-Translator: Filip <PERSON> <<EMAIL>>\n"
"Language-Team: Czech <>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"
"X-Generator: Gtranslator 2.91.7\n"

#: account/adapter.py:51
msgid "Username can not be used. Please use other username."
msgstr "Toto uživatelské jméno nemůže být zvoleno. Prosím, zvolte si jiné."

#: account/adapter.py:57
msgid "Too many failed login attempts. Try again later."
msgstr "Příliš mnoho pokusů o přihlášení. Zkuste to prosím později."

#: account/adapter.py:59
msgid "A user is already registered with this email address."
msgstr "Uživatel s tímto e-mailem je již registrován."

#: account/adapter.py:60
msgid "Incorrect password."
msgstr "Nesprávné heslo."

#: account/adapter.py:340
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Heslo musí obsahovat minimálně {0} znaků."

#: account/apps.py:9
msgid "Accounts"
msgstr "Účty"

#: account/forms.py:61 account/forms.py:445
msgid "You must type the same password each time."
msgstr "Hesla se musí shodovat."

#: account/forms.py:93 account/forms.py:408 account/forms.py:547
#: account/forms.py:685
msgid "Password"
msgstr "Heslo"

#: account/forms.py:94
msgid "Remember Me"
msgstr "Zapamatovat"

#: account/forms.py:98
msgid "This account is currently inactive."
msgstr "Účet je v tuto chvíli neaktivní."

#: account/forms.py:100
msgid "The email address and/or password you specified are not correct."
msgstr "Zadaný e-mail nebo heslo není správné."

#: account/forms.py:103
msgid "The username and/or password you specified are not correct."
msgstr "Zadané uživatelské jméno nebo heslo není správné."

#: account/forms.py:114 account/forms.py:283 account/forms.py:472
#: account/forms.py:567
msgid "Email address"
msgstr "E-mailová adresa"

#: account/forms.py:118 account/forms.py:321 account/forms.py:469
#: account/forms.py:562
msgid "Email"
msgstr "E-mail"

#: account/forms.py:121 account/forms.py:124 account/forms.py:273
#: account/forms.py:276
msgid "Username"
msgstr "Uživatelské jméno"

#: account/forms.py:134
msgid "Username or email"
msgstr "Uživatelské jméno nebo e-mail"

#: account/forms.py:137
msgctxt "field label"
msgid "Login"
msgstr "Přihlášení"

#: account/forms.py:148
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Zapomenuté heslo?"

#: account/forms.py:312
msgid "Email (again)"
msgstr "E-mail (znovu)"

#: account/forms.py:316
msgid "Email address confirmation"
msgstr "Potrvzení e-mailové adresy"

#: account/forms.py:324
msgid "Email (optional)"
msgstr "E-mail (nepovinné)"

#: account/forms.py:379
msgid "You must type the same email each time."
msgstr "Vložené e-maily se musí shodovat."

#: account/forms.py:414 account/forms.py:550
msgid "Password (again)"
msgstr "Heslo (znovu)"

#: account/forms.py:484
msgid "This email address is already associated with this account."
msgstr "Tento e-mail je již k tomuto účtu přiřazen."

#: account/forms.py:486
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Nelze přidat více než %d e-mailových adres."

#: account/forms.py:524
msgid "Current Password"
msgstr "Současné heslo"

#: account/forms.py:527 account/forms.py:634
msgid "New Password"
msgstr "Nové heslo"

#: account/forms.py:530 account/forms.py:635
msgid "New Password (again)"
msgstr "Nové heslo (znovu)"

#: account/forms.py:538
msgid "Please type your current password."
msgstr "Prosím, zadejte svoje současné heslo."

#: account/forms.py:579
msgid "The email address is not assigned to any user account"
msgstr "E-mail není přiřazen k žádnému účtu"

#: account/forms.py:655
msgid "The password reset token was invalid."
msgstr "Token pro reset hesla není platný."

#: account/models.py:21
msgid "user"
msgstr "uživatel"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-mailová adresa"

#: account/models.py:28
msgid "verified"
msgstr "ověřeno"

#: account/models.py:29
msgid "primary"
msgstr "primární"

#: account/models.py:35
msgid "email addresses"
msgstr "e-mailové adresy"

#: account/models.py:141
msgid "created"
msgstr "vytvořeno"

#: account/models.py:142
msgid "sent"
msgstr "odeslaný"

#: account/models.py:143 socialaccount/models.py:65
msgid "key"
msgstr "klíč"

#: account/models.py:148
msgid "email confirmation"
msgstr "Potvrzovací e-mail"

#: account/models.py:149
msgid "email confirmations"
msgstr "Ověřovací e-maily"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Nemůžete aktivovat dvoufaktorovou autentizaci, dokud nepotvrdíte svoue-"
"mailovou adresu."

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Nelze přidat e-mailovou adresu k účtu chráněnému dvoufaktorovouautentizací."

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr "Nesprávný kód."

#: mfa/apps.py:7
msgid "MFA"
msgstr "2FA"

#: mfa/forms.py:15 mfa/forms.py:17
msgid "Code"
msgstr "Kód"

#: mfa/forms.py:48
msgid "Authenticator code"
msgstr "Kód autentifikátoru"

#: mfa/models.py:15
msgid "Recovery codes"
msgstr "Záchranné kódy"

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr "TOTP Autentifikátor"

#: socialaccount/adapter.py:32
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Účet s touto e-mailovou adresou již existuje. Prosím přihlaste se nejdříve "
"pod tímto účtem a potom připojte svůj %s účet."

#: socialaccount/adapter.py:138
msgid "Your account has no password set up."
msgstr "Váš účet nemá nastavené heslo."

#: socialaccount/adapter.py:145
msgid "Your account has no verified email address."
msgstr "Váš účet nemá žádný ověřený e-mail."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Účty sociálních sítí"

#: socialaccount/models.py:39 socialaccount/models.py:93
msgid "provider"
msgstr "poskytovatel"

#: socialaccount/models.py:48
msgid "provider ID"
msgstr "ID poskytovatele"

#: socialaccount/models.py:52
msgid "name"
msgstr "jméno"

#: socialaccount/models.py:54
msgid "client id"
msgstr "id klienta"

#: socialaccount/models.py:56
msgid "App ID, or consumer key"
msgstr "App ID nebo uživatelský klíč"

#: socialaccount/models.py:59
msgid "secret key"
msgstr "tajný klíč"

#: socialaccount/models.py:62
msgid "API secret, client secret, or consumer secret"
msgstr "tajný API klíč, tajný klientský klíč nebo uživatelský tajný klíč"

#: socialaccount/models.py:65
msgid "Key"
msgstr "Klíč"

#: socialaccount/models.py:77
msgid "social application"
msgstr "sociální aplikace"

#: socialaccount/models.py:78
msgid "social applications"
msgstr "sociální aplikace"

#: socialaccount/models.py:113
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:115
msgid "last login"
msgstr "poslední přihlášení"

#: socialaccount/models.py:116
msgid "date joined"
msgstr "datum registrace"

#: socialaccount/models.py:117
msgid "extra data"
msgstr "extra data"

#: socialaccount/models.py:121
msgid "social account"
msgstr "účet sociální sítě"

#: socialaccount/models.py:122
msgid "social accounts"
msgstr "účty sociálních sítí"

#: socialaccount/models.py:156
msgid "token"
msgstr "token"

#: socialaccount/models.py:157
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) nebo přístupový token (OAuth2)"

#: socialaccount/models.py:161
msgid "token secret"
msgstr "tajný token"

#: socialaccount/models.py:162
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) nebo token pro obnovu (OAuth2)"

#: socialaccount/models.py:165
msgid "expires at"
msgstr "vyprší"

#: socialaccount/models.py:170
msgid "social application token"
msgstr "token sociální aplikace"

#: socialaccount/models.py:171
msgid "social application tokens"
msgstr "tokeny sociálních aplikací"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Neplatná data profilu"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Chyba při odesílání požadavku: \"%s\". Odpoveď byla: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Chyba při získávání přístupového klíče od \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Není uložen žádný požadavkový klíč pro: \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Není uložen žádný přístupový klíč pro: \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Není přístup k privátním zdrojům: \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Chyba při získávání požadavkového klíče \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Neaktivní účet"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Tento účet není aktivní."

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mailové adresy"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "K vašemu účtu jsou přiřazeny tyto e-mailové adresy:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Ověřeno"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Neověřeno"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Primární"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Zvolit jako primární"

#: templates/account/email.html:45 templates/account/email_change.html:29
msgid "Re-send Verification"
msgstr "Znovu zaslat oveřovací e-mail"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Odstranit"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Přidat e-mailovou adresu"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Přidat e-mail"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Opravdu chcete odstranit zvolené e-mailové adresy?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Dostáváte tento e-mail, protože vy nebo někdo jiný se pokusil registrovat "
"účet s \n"
"použitím e-mailové adresy:\n"
"\n"
"%(email)s\n"
"\n"
"Ale účet s touto e-mailovou adresou již existuje. Pokud jste na to "
"zapomněli, \n"
"použijte prosím postup obnovení hesla k obnovení vašeho účtu:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Účet již existuje."

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Pozdrav z %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Děkujeme, že používáte %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Tento e-mail jste obdrželi protože uživatel %(user_display)s zadal vaši "
"adresu jako e-mailovou adresu pro připojení svého účtu na.na stránkách "
"%(site_domain)s.\n"
"\n"
"Pro potvrzení, že je to v pořádku, pokračujte na %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Potvrďte prosím svou e-mailovou adresu"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Tento e-mail jste obdrželi protože jste vy nebo někdo jiný zažádal o změnu "
"hesla uživatelského účtu.\n"
"Pokud jste to nebyli vy, můžete tento e-mail ignorovat. Pokud ano, klikněte "
"na odkaz níže pro změnu vašeho hesla."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""
"Pro případ, že byste zapomněli, vaše uživatelské jméno je %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail pro reset hesla"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Tento e-mail jste obdrželi protože jste vy nebo někdo jiný zažádal o změnu \n"
"hesla uživatelského účtu. Nicméně, nemáme žádný záznam o uživateli s \n"
"e-mailem %(email)s v naší databázi.\n"
"\n"
"Tento e-mail můžete bezpečně ignorovat, pokud jste nezažádali o změnu "
"hesla.\n"
"\n"
"Jestliže jste to byli vy, můžete se zaregistrovat na stránkách pomocí "
"odkazu \n"
"níže."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-mailové adresa"

#: templates/account/email_change.html:14
msgid "The following email address is associated with your account:"
msgstr "K vašemu účtu je přiřazena tato e-mailová adresa:"

#: templates/account/email_change.html:19
msgid "Your email address is still pending verification:"
msgstr "Vaše primární e-mailová adresa stále čeká na ověření:"

#: templates/account/email_change.html:38
msgid "Change Email Address"
msgstr "Změna e-mailové adresy"

#: templates/account/email_change.html:49
#: templates/allauth/layouts/base.html:29
msgid "Change Email"
msgstr "Změnit E-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Potvrzení e-mailové adresy"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prosím, potvrďte, že <a href=\"mailto:%(email)s\">%(email)s</a> je e-mailová "
"adresa pro uživatele %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:28
msgid "Confirm"
msgstr "Potvrdit"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Nelze potvrdit %(email)s, protože již byl spojen s jiným účtem."

#: templates/account/email_confirm.html:35
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Tento ověřovací odkaz již vypršel nebo není správný. Prosím, <a href="
"\"%(email_url)s\">zažádejte si o nový</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Přihlásit se"

#: templates/account/login.html:12
#, fuzzy, python-format
#| msgid ""
#| "If you have not created an account yet, then please\n"
#| "<a href=\"%(signup_url)s\">sign up</a> first."
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Pokud jste si ještě nevytvořili účet, nejprve se <a href=\"%(signup_url)s"
"\">zaregistrujte</a>."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
msgid "Sign Out"
msgstr "Odhlásit se"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Jste si jisti, že se chcete odhlásit?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Nemůžete odstranit primární e-mailovou adresu (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Ověření e-mailu posláno na %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Ověřili jste %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mailová adresa %(email)s byla odebrána."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Úspěšně přihlášen jako %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Právě jste byl odhlášen."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Heslo bylo úspěšně změněno."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Heslo bylo úspěšně nastaveno."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primární e-mail byla nastavena."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Vaše primární e-mailová adresa musí být ověřena."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Změnit heslo"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Zapomenuté heslo?"

#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Reset hesla"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Zapomněli jste heslo? Zadejte prosím svoji e-mailovou adresu a do e-mailové "
"schránky Vám přijde návod na jeho obnovu."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Resetovat moje heslo"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Prosím, kontaktujte nás, pokud máte jakékoliv potíže s resetováním hesla."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Poslali jsme vám e-mail. Pokud jste ho neobdrželi, zkontrolujte prosím "
"složku s nevyžádanou poštou (spam). V opačném případě nás kontaktujte, pokud "
"ho neobdržíte do několika minut."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Chybný klíč"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Odkaz pro změnu hesla není platný, protože již byl použit. Prosím, <a href="
"\"%(passwd_reset_url)s\">zažádejte si o nové."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Vaše heslo bylo změněno."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Nastavit heslo"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
msgid "Confirm Access"
msgstr "Potvrdit přístup"

#: templates/account/reauthenticate.html:12
msgid "To safeguard the security of your account, please enter your password:"
msgstr "Pro zabezpečení vašeho účtu, prosím, zadejte vaše heslo:"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Zaregistrovat se"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Zaregistrovat se"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Máte již účet? <a href=\"%(login_url)s\">Přihlašte se</a>, prosím."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registrace je uzavřena"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Omlouváme se, ale registrace je momentálně uzavřena."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Poznámka"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "momentálně jste přihlášen jako %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Varování:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"V současné chvíli nemáte nastaveny žádné e-mailové adresy. Prosím, uložte si "
"k účtu alespoň jeden e-mail, abyste moli dostávat upozornění nebo mohli "
"použít funkci zapomenutého hesla apod."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Ověřte svoji e-mailovou adresu."

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Byl vám zaslán ověřovací e-mail. Následujte odkaz v e-mailu pro dokončení "
"registračního procesu. Pokud jste ho neobdrželi, zkontrolujte prosím složku "
"s nevyžádanou poštou (spam). Neváhejte nás kontaktovat v případě, pokud e-"
"mail do několika minut neobdržíte."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Tato část stránek vyžaduje ověření,\n"
"že jste ten, kdo tvrdíte. K těmto účelům požadujeme\n"
"ověření vlastnictví vaší e-mailové adresy."

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Zaslali jsme na vaši e-mailovou adresu\n"
"ověřovací e-mail. Prosím, klikněte na odkaz uvnitř e-mailu.\n"
"Pokud jste ho neobdrželi, zkontrolujte prosím složku s nevyžádanou poštou "
"(spam). \n"
"Neváhejte nás kontaktovat v případě, pokud e-mail nedostanete do několika "
"minut."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Poznámka:</strong> stále můžete <a href=\"%(email_url)s\">změnit "
"vaši e-mailovou adresu.</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Zprávy:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Dvoufaktorová autentizace"

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Váš účet je chráněn dvoufaktorovou autentizací. Prosím, zadejte autentizační "
"kód:"

#: templates/mfa/index.html:13 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Autentifikátor"

#: templates/mfa/index.html:17
msgid "Authentication using an authenticator app is active."
msgstr "Autentizace pomocí autentifikátoru je aktivní."

#: templates/mfa/index.html:19
msgid "An authenticator app is not active."
msgstr "Autentifikátor není aktivní."

#: templates/mfa/index.html:27 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktivovat"

#: templates/mfa/index.html:31 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktivovat"

#: templates/mfa/index.html:39 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Záchranné kódy"

#: templates/mfa/index.html:44 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Z dostupných záchranných kódů je použito %(unused_count)s z celkového počtu "
"%(total_count)s kódů."
msgstr[1] ""
"Z dostupných záchranných kódů je použito %(unused_count)s z celkového počtu "
"%(total_count)s kódů."
msgstr[2] ""
"Z dostupných záchranných kódů je použito %(unused_count)s z celkového počtu "
"%(total_count)s kódů."
msgstr[3] ""
"Z dostupných záchranných kódů je použito %(unused_count)s z celkového počtu "
"%(total_count)s kódů."

#: templates/mfa/index.html:47
msgid "No recovery codes set up."
msgstr "Nejsou nastaveny žádné záchranné kódy."

#: templates/mfa/index.html:56
msgid "View"
msgstr ""

#: templates/mfa/index.html:62
#, fuzzy
#| msgid "Download codes"
msgid "Download"
msgstr "Stáhnout kódy"

#: templates/mfa/index.html:70 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generovat"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Byla vygenerována nová sada záchranných kódů."

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Autentifikátor byl aktivován."

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Autentifikátor byl deaktivován."

#: templates/mfa/recovery_codes/generate.html:9
#, fuzzy
#| msgid ""
#| "You are about to generate a new set of recovery codes for your account. "
#| "This action will invalidate your existing codes. Are you sure?"
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Chystáte se vygenerovat novou sadu záchranných kódů pro váš účet. Tato akce "
"zneplatní vaše stávající kódy. Jste si jisti?"

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
#, fuzzy
#| msgid "View codes"
msgid "Unused codes"
msgstr "Zobrazit kódy"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Stáhnout kódy"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Generovat nové kódy"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Aktivovat Autentifikátor"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Klíč autentifikátoru"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deaktivovat Autentifikátor"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Chystáte se deaktivovat autentizaci pomocí autentifikátoru. Jste si jisti?"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Social Network Login Failure"
msgstr "Přihlášení pomocí sociální sítě selhalo."

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "Nastala chyba při přihlašování pomocí vašeho účtu sociální sítě."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Propojení účtu"

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Můžete se přihlásit pomocí jakéhokoliv následujícího účtu:"

#: templates/socialaccount/connections.html:45
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "V současné chvíli nemáte připojeny žádné účty sociálních sítí."

#: templates/socialaccount/connections.html:48
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Přidejte další účet"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Připojit %(provider)s"

#: templates/socialaccount/login.html:13
#, fuzzy, python-format
#| msgid ""
#| "You are about to connect a new third party account from %(provider)s."
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Chystáte se připojit nový účet třetí strany od %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Přihlásit se pomocí %(provider)s"

#: templates/socialaccount/login.html:20
#, fuzzy, python-format
#| msgid ""
#| "You are about to sign in using a third party account from %(provider)s."
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Chystáte se přihlásit pomocí účtu třetí strany od %(provider)s."

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "Pokračovat"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Přihlášení zrušeno"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Rozhodli jste se zrušit přihlašování jednoho z vašich účtů. Pokud je to "
"omylem, následujte <a href=\"%(login_url)s\">přihlášení</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Účet sociální sítě byl připojen."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Účet sociální sítě je již spojen s jiným účtem."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Účet sociální sítě byl odpojen."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Chystáte se použít vášeho %(provider_name)s účtu k přihlášení na naše "
"stránky \n"
"%(site_name)s. Jako poslední krok, prosím, vyplňte následující formulář:"

#: templates/socialaccount/snippets/login.html:8
msgid "Or use a third-party"
msgstr ""

#, fuzzy
#~| msgid "Generate"
#~ msgid "Regenerate"
#~ msgstr "Generovat"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a href=\"%(signup_url)s"
#~ "\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Přihlašte se prosím výběrem jednoho\n"
#~ "z vašich účtů třetích stran. Nebo se <a href=\"%(signup_url)s"
#~ "\">zaregistruje</a> na stránky %(site_name)s a přihlašte se níže:"

#~ msgid "or"
#~ msgstr "nebo"

#~ msgid "change password"
#~ msgstr "změnit heslo"

#~ msgid "OpenID Sign In"
#~ msgstr "Přihlášení OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Tento e-mail je již přiřazen k jinému účtu."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Zaslali jsme vám e-mail. Prosím, kontaktujte nás, pokud ho nedostanete do "
#~ "několika minut."

#~ msgid "Account"
#~ msgstr "Účet"

#~| msgid "The password reset token was invalid."
#~ msgid "The provided password is not valid."
#~ msgstr "Použité heslo není platné."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Zadané přihlašovací údaje nejsou správné."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Uživatelské jméno může obsahovat pouze písmena, číslice a znaky @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Toto uživatelské jméno je již zvoleno. Prosím, vyberte si jiné."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Přihlásit se"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Potvrdili jste e-mailovou adresu <a href=\"mailto:%(email)s\">%(email)s</"
#~ "a> uživateli %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Děkujeme za využívání našich stránek!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Ověřovací e-mail byl zaslán: %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Smazat heslo"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Můžete si smazat heslo, protože používáte jiné způsoby přihlášení."

#~ msgid "delete my password"
#~ msgstr "Odstanit moje heslo"

#~ msgid "Password Deleted"
#~ msgstr "Heslo bylo odstraněno"

#~ msgid "Your password has been deleted."
#~ msgstr "Vaše heslo bylo smazáno."
