# tests/test_user_creation.py

from django.test import TestCase
from django.contrib.auth import get_user_model
from users.models import Role

class UserCreationTest(TestCase):
    def setUp(self):
        # Create a role for the user
        self.role = Role.objects.create(role_name='CLIENT', description='Client role')

    def test_create_user(self):
        User = get_user_model()
        user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='&(!6af673b#@43a66',
            role=self.role
        )
        self.assertIsNotNone(user.user_id)
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.name, 'Test User')
        self.assertTrue(user.check_password('&(!6af673b#@43a66'))
        self.assertEqual(user.role, self.role)