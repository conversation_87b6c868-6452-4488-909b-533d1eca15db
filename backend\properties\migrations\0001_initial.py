# Generated by Django 5.1.7 on 2025-03-22 12:30

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Property',
            fields=[
                ('property_id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('location', models.CharField(max_length=255)),
                ('size', models.DecimalField(decimal_places=2, help_text='Size in square meters', max_digits=10)),
                ('type', models.CharField(choices=[('HOUSE', 'House'), ('APARTMENT', 'Apartment'), ('CONDO', 'Condominium'), ('LAND', 'Land'), ('COMMERCIAL', 'Commercial')], max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('agent', models.ForeignKey(limit_choices_to={'role__role_name': 'AGENT'}, on_delete=django.db.models.deletion.PROTECT, related_name='properties', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Properties',
                'db_table': 'properties',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Inquiry',
            fields=[
                ('inquiry_id', models.AutoField(primary_key=True, serialize=False)),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('CONTACTED', 'Contacted'), ('VIEWED', 'Viewed'), ('CLOSED', 'Closed')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(limit_choices_to={'role__role_name': 'CLIENT'}, on_delete=django.db.models.deletion.PROTECT, related_name='inquiries', to=settings.AUTH_USER_MODEL)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='inquiries', to='properties.property')),
            ],
            options={
                'verbose_name_plural': 'Inquiries',
                'db_table': 'property_inquiries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Image',
            fields=[
                ('image_id', models.AutoField(primary_key=True, serialize=False)),
                ('image', models.ImageField(upload_to='properties/%Y/%m/%d/')),
                ('caption', models.CharField(blank=True, max_length=255)),
                ('is_primary', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='properties.property')),
            ],
            options={
                'db_table': 'property_images',
                'ordering': ['-is_primary', '-created_at'],
            },
        ),
    ]
